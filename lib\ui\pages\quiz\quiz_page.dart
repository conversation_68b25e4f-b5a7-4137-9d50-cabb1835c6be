import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';
import 'package:lottie/lottie.dart';
import 'package:note_x/lib.dart';
import 'package:note_x/ui/pages/quiz/cubit/quiz_state.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import '../../../base/base_page_state.dart';

class QuizPage extends StatefulWidget {
  final List<QuizModelView> quizzes;
  final String? audioFilePath;
  final String? audioUrl;
  final bool isCommunityNote;
  final bool isGeneratingQuiz;

  const QuizPage({
    super.key,
    required this.quizzes,
    this.audioFilePath,
    this.audioUrl,
    required this.isCommunityNote,
    this.isGeneratingQuiz = false,
  });

  @override
  State<QuizPage> createState() => _QuizPageState();
}

class _QuizPageState extends BasePageStateDelegate<QuizPage, QuizCubit> {
  late PageController controller;
  int currentPage = 0;
  final ValueNotifier<int> _currentIndexNotifier = ValueNotifier<int>(0);
  final ValueNotifier<bool> _isQuestionSide = ValueNotifier<bool>(true);
  final ValueNotifier<bool> _canMoveToNextNotifier = ValueNotifier<bool>(false);
  final AppCubit appCubit = GetIt.instance.get<AppCubit>();
  TextEditingController editController = TextEditingController();

  @override
  void initState() {
    super.initState();
    cubit.init(widget.quizzes);
    controller = PageController(keepPage: true, initialPage: 0);
    AnalyticsService.logEventScreenView(
      screenClass: EventScreenClass.noteDetailPage,
      screenName: EventScreenName.note_quizzes,
    );
  }

  @override
  void didUpdateWidget(QuizPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.quizzes != oldWidget.quizzes) {
      cubit.init(widget.quizzes);
      _currentIndexNotifier.value = 0;
      _isQuestionSide.value = true;
      _canMoveToNextNotifier.value = false;
      currentPage = 0;
      if (controller.hasClients) {
        controller.jumpToPage(0);
      }
    }
  }

  @override
  void dispose() {
    controller.dispose();
    _currentIndexNotifier.dispose();
    _isQuestionSide.dispose();
    _canMoveToNextNotifier.dispose();
    super.dispose();
  }

  bool _canMoveToNextQuestion(int currentIndex) {
    return cubit.quizzes[currentIndex].isAnswered;
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: Stack(
        children: [
          Padding(
            padding: EdgeInsets.only(top: 16.w),
            child: Column(
              children: [
                cubit.appCubit.isTablet
                    ? AppConstants.kSpacingItem0
                    : AppConstants.kSpacingItem16,
                Flexible(
                  flex: 6,
                  child: widget.isGeneratingQuiz
                      ? Center(
                          child: Stack(
                            children: [
                              Container(
                                margin: EdgeInsets.symmetric(
                                  horizontal: context.isTablet ? 70.w : 16.w,
                                ),
                                child: Lottie.asset(
                                  Assets.videos.starGenerate,
                                  width: 200,
                                  height: 200,
                                ),
                              ),
                              Positioned.fill(
                                bottom: 0,
                                child: Align(
                                  alignment: Alignment.bottomCenter,
                                  child: AnimatedTextEffect(
                                    text: S.current.gen_ai,
                                    style: TextStyle(
                                      color: AppColors.white,
                                      fontSize: context.isTablet ? 16 : 14.sp,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        )
                      : PageView.builder(
                          controller: controller,
                          itemCount: cubit.quizzes.length,
                          onPageChanged: (index) {
                            _currentIndexNotifier.value = index;
                            if (index > currentPage) {
                              cubit.onNextQuiz();
                            }
                            _canMoveToNextNotifier.value =
                                _canMoveToNextQuestion(index);
                            currentPage = index;
                          },
                          itemBuilder: (context, quizIndex) {
                            return Card(
                              shape: RoundedRectangleBorder(
                                side: BorderSide(
                                  color: context.colorScheme.mainNeutral
                                      .withOpacity(0.1),
                                  width: 2.0.w,
                                ),
                                borderRadius: BorderRadius.circular(20.0.r),
                              ),
                              margin: EdgeInsets.symmetric(
                                horizontal:
                                    cubit.appCubit.isTablet ? 60.w : 16.w,
                              ),
                              child: Container(
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    begin: Alignment.bottomCenter,
                                    end: Alignment.center,
                                    colors: context.isDarkMode
                                        ? AppColors.quizFlashcardGradientList
                                        : AppColors
                                            .quizFlashcardGradientListLighMode,
                                  ),
                                  borderRadius: BorderRadius.circular(20.0.r),
                                ),
                                child: Padding(
                                  padding: EdgeInsets.only(
                                    left: 20.w,
                                    right: 20.w,
                                  ),
                                  child: SingleChildScrollView(
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        AppConstants.kSpacingItem34,
                                        CommonText(
                                          '${S.current.question} ${(quizIndex + 1).toString()} ${S.current.of_index} ${cubit.quizzes.length}',
                                          style: TextStyle(
                                            fontSize: cubit.appCubit.isTablet
                                                ? 18
                                                : 16.sp,
                                            fontWeight: FontWeight.w700,
                                            color: context.colorScheme.mainBlue,
                                          ),
                                        ),
                                        AppConstants.kSpacingItem4,
                                        CommonText(
                                          widget.quizzes[quizIndex].question,
                                          style: TextStyle(
                                            fontSize: cubit.appCubit.isTablet
                                                ? 22
                                                : 20.sp,
                                            color: AppColors
                                                .insideContainerTextColor,
                                            fontWeight: FontWeight.w600,
                                          ),
                                          textAlign: TextAlign.center,
                                        ),
                                        AppConstants.kSpacingItem16,
                                        ListView.builder(
                                            physics:
                                                const NeverScrollableScrollPhysics(),
                                            shrinkWrap: true,
                                            itemCount: widget.quizzes[quizIndex]
                                                .answers.length,
                                            itemBuilder: (context, index) {
                                              return BlocBuilder<QuizCubit,
                                                  QuizState>(
                                                buildWhen:
                                                    (previous, current) =>
                                                        previous.chosenQuiz !=
                                                        current.chosenQuiz,
                                                builder: (context, state) {
                                                  return Padding(
                                                    padding: EdgeInsets.only(
                                                      bottom: 16.h,
                                                    ),
                                                    child: _buildAnswerQuiz(
                                                      quiz: cubit
                                                          .quizzes[quizIndex],
                                                      answerIndex: index,
                                                      quizIndex: quizIndex,
                                                    ),
                                                  );
                                                },
                                              );
                                            }),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                ),
                AppConstants.kSpacingItem200,
              ],
            ),
          ),

          /// Status Question
          Positioned(
            left: 0,
            right: 0,
            child: widget.isGeneratingQuiz
                ? const SizedBox.shrink()
                : ValueListenableBuilder<int>(
                    valueListenable: _currentIndexNotifier,
                    builder: (context, currentIndex, _) {
                      return BlocBuilder<QuizCubit, QuizState>(
                        buildWhen: (previous, current) =>
                            previous.chosenQuiz != current.chosenQuiz,
                        builder: (context, state) {
                          final currentQuiz = widget.quizzes[currentIndex];
                          String imageAsset = Assets.videos.detailQuestion;

                          if (currentQuiz.isAnswered) {
                            if (currentQuiz.answerChosenIndex ==
                                currentQuiz.correctIndex) {
                              imageAsset = Assets.videos.detailAnswerTrue;
                            } else {
                              imageAsset = Assets.videos.detailAnswerFalse;
                            }
                          }

                          return Lottie.asset(imageAsset,
                              width: 64, height: 64);
                        },
                      );
                    },
                  ),
          ),

          /// Transcript Context

          Positioned(
            left: 0,
            right: 0,
            bottom: 100.h,
            child: widget.isGeneratingQuiz
                ? const SizedBox.shrink()
                : ValueListenableBuilder<int>(
                    valueListenable: _currentIndexNotifier,
                    builder: (context, currentIndex, _) {
                      return BlocBuilder<QuizCubit, QuizState>(
                        buildWhen: (previous, current) =>
                            previous.chosenQuiz != current.chosenQuiz,
                        builder: (context, state) {
                          final currentQuiz = widget.quizzes[currentIndex];
                          return currentQuiz.context.isNotEmpty &&
                                  currentQuiz.isAnswered
                              ? ShadowedTextButton(
                                  onPressed: () {
                                    showModalBottomSheetTranscriptContext(
                                      context,
                                      widget.audioFilePath ?? '',
                                      widget.audioUrl ?? '',
                                      currentQuiz.context,
                                      widget.isCommunityNote,
                                    );
                                  },
                                )
                              : const SizedBox.shrink();
                        },
                      );
                    },
                  ),
          ),

          Positioned(
            left: 0,
            right: 0,
            bottom: 100.h,
            child: ValueListenableBuilder<int>(
              valueListenable: _currentIndexNotifier,
              builder: (context, currentIndex, _) {
                return Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  child: ValueListenableBuilder<int>(
                    valueListenable: _currentIndexNotifier,
                    builder: (context, currentIndex, _) {
                      return BlocBuilder<QuizCubit, QuizState>(
                        buildWhen: (previous, current) =>
                            previous.chosenQuiz != current.chosenQuiz,
                        builder: (context, state) {
                          final currentQuiz = widget.quizzes[currentIndex];
                          return currentQuiz.context.isNotEmpty &&
                                  currentQuiz.isAnswered
                              ? ShadowedTextButton(
                                  onPressed: () {
                                    showModalBottomSheetTranscriptContext(
                                      context,
                                      widget.audioFilePath ?? '',
                                      widget.audioUrl ?? '',
                                      currentQuiz.context,
                                      widget.isCommunityNote,
                                    );
                                  },
                                )
                              : const SizedBox.shrink();
                        },
                      );
                    },
                  ),
                );
              },
            ),
          ),

          Positioned(
            left: 0,
            right: 0,
            bottom: 176.h,
            child: Center(
              child: widget.isGeneratingQuiz
                  ? const SizedBox.shrink()
                  : SmoothPageIndicator(
                      controller: controller,
                      count: cubit.quizzes.length,
                      effect: ScrollingDotsEffect(
                        activeDotColor: context.colorScheme.mainBlue,
                        dotColor: context.colorScheme.mainNeutral,
                        dotHeight: 6.h,
                        dotWidth: 6.w,
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnswerQuiz({
    required QuizModelView quiz,
    required int answerIndex,
    required int quizIndex,
  }) {
    bool isChosenAnswer = answerIndex == quiz.answerChosenIndex;
    bool isCorrectAnswer = answerIndex == quiz.correctIndex;
    Color titleColor;
    Color textColor;

    if (quiz.isAnswered) {
      if (isCorrectAnswer) {
        titleColor = AppColors.primaryBlue;
        textColor = context.colorScheme.themeWhite;
      } else if (isChosenAnswer) {
        titleColor = AppColors.primaryYellow;
        textColor = context.colorScheme.themeWhite;
      } else {
        titleColor = context.colorScheme.mainPrimary.withOpacity(0.1);
        textColor = context.colorScheme.themeWhite;
      }
    } else {
      titleColor = context.colorScheme.mainPrimary.withOpacity(0.1);
      textColor = context.colorScheme.themeWhite;
    }

    return GestureDetector(
      onTap: quiz.isAnswered
          ? null
          : () {
              cubit.onChooseAnswer(
                quizIndex: quizIndex,
                answerChosenIndex: answerIndex,
              );

              _canMoveToNextNotifier.value = true;
              final totalQuestions = widget.quizzes.length;
              final correctAnswers = cubit.quizzes
                  .where((quiz) =>
                      quiz.isAnswered &&
                      quiz.answerChosenIndex == quiz.correctIndex)
                  .length;
              final wrongAnswers = cubit.quizzes
                  .where((quiz) =>
                      quiz.isAnswered &&
                      quiz.answerChosenIndex != quiz.correctIndex)
                  .length;

              if (correctAnswers + wrongAnswers == totalQuestions) {
                Timer(const Duration(seconds: 1), () {
                  if (mounted) {
                    _showQuizCongratulationDialog();
                  }
                });
              } else {
                Timer(const Duration(seconds: 2), () {
                  if (mounted && controller.hasClients) {
                    final currentPageIndex = controller.page?.round() ?? 0;
                    if (currentPageIndex == quizIndex) {
                      controller.nextPage(
                        duration: const Duration(milliseconds: 500),
                        curve: Curves.linearToEaseOut,
                      );
                      _canMoveToNextNotifier.value = false;
                    }
                  }
                });
              }
            },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(32.r),
          color: titleColor,
        ),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(32.r),
            boxShadow: [
              if (context.isDarkMode)
                BoxShadow(
                  color: Colors.black.withOpacity(0.25),
                  blurRadius: 4,
                  spreadRadius: 0,
                  offset: const Offset(2, 4),
                ),
              if (titleColor == AppColors.primaryBlue ||
                  titleColor == AppColors.primaryYellow)
                BoxShadow(
                  color: Colors.white.withOpacity(0.1),
                  blurRadius: 14,
                  spreadRadius: -2,
                  offset: const Offset(-2, -4),
                ),
            ],
          ),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  MyUtils.mapIndexToValue(answerIndex),
                  style: TextStyle(
                    fontSize: cubit.appCubit.isTablet ? 18 : 15.sp,
                    fontWeight: FontWeight.w700,
                    color: textColor,
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: Text(
                    quiz.answers[answerIndex],
                    style: TextStyle(
                      fontSize: cubit.appCubit.isTablet ? 18 : 15.sp,
                      fontWeight: FontWeight.w400,
                      color: textColor,
                    ),
                  ),
                ),
                const SizedBox.shrink()
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showQuizCongratulationDialog() {
    final totalQuestions = widget.quizzes.length;
    final correctAnswers = cubit.quizzes
        .where((quiz) =>
            quiz.isAnswered && quiz.answerChosenIndex == quiz.correctIndex)
        .length;
    final wrongAnswers = cubit.quizzes
        .where((quiz) =>
            quiz.isAnswered && quiz.answerChosenIndex != quiz.correctIndex)
        .length;

    CommonDialogs.showQuizCongratulationDialog(
      context,
      S.current.quiz_score,
      () {
        final lastQuizIndex = widget.quizzes.length - 1;
        controller.animateToPage(
          lastQuizIndex,
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeInOut,
        );
      },
      totalQuestions: totalQuestions,
      correctAnswers: correctAnswers,
      wrongAnswers: wrongAnswers,
    );
  }

  /// Checks if the current card is the first card
  bool isFirstCard(int index) {
    return index == 0;
  }

  /// Checks if the current card is the last card
  bool isLastCard(int index) {
    return index == widget.quizzes.length - 1;
  }

  /// Returns the appropriate left navigation icon based on card position and theme
  String getLeftNavigationIcon(int currentIndex) {
    if (isFirstCard(currentIndex)) {
      // First card - disabled icon
      return appCubit.isReverseView
          ? context.isDarkMode
              ? Assets.icons.icRightCircleBlack
              : Assets.icons.icRightCircleLightMode
          : context.isDarkMode
              ? Assets.icons.icLeftCircleBlack
              : Assets.icons.icLeftCircleBackLightMode;
    } else {
      // Not first card - enabled icon
      return appCubit.isReverseView
          ? Assets.icons.icRightCircle
          : Assets.icons.icLeftCircle;
    }
  }

  /// Returns the appropriate right navigation icon based on card position, theme, and whether can move next
  String getRightNavigationIcon(int currentIndex, bool canMoveNext) {
    if (isLastCard(currentIndex)) {
      // Last card - disabled icon
      return appCubit.isReverseView
          ? context.isDarkMode
              ? Assets.icons.icLeftCircleBlack
              : Assets.icons.icLeftCircleBackLightMode
          : context.isDarkMode
              ? Assets.icons.icRightCircleBlack
              : Assets.icons.icRightCircleLightMode;
    } else {
      // Not last card
      if (appCubit.isReverseView) {
        return Assets.icons.icLeftCircle;
      } else {
        // Check if can move to next
        return canMoveNext
            ? Assets.icons.icRightCircle
            : context.isDarkMode
                ? Assets.icons.icRightCircleBlack
                : Assets.icons.icRightCircleLightMode;
      }
    }
  }
}
