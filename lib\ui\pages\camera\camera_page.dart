import 'dart:io';
import 'dart:async';

import 'package:el_tooltip/el_tooltip.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:lottie/lottie.dart';
import 'package:note_x/lib.dart';
import 'package:note_x/base/base_page_state.dart';
import 'cubit/camera_state.dart';
import 'package:get_it/get_it.dart';

class CameraPage extends StatefulWidget {
  static const routeName = 'CameraPage';

  final VoidCallback? onClose;
  final VoidCallback? onCreateNoteSuccess;
  final bool isFromWelcome;
  final bool autoTakePhoto;

  const CameraPage({
    super.key,
    this.onClose,
    this.onCreateNoteSuccess,
    this.isFromWelcome = false,
    this.autoTakePhoto = true,
  });

  @override
  State<CameraPage> createState() => _CameraPageState();
}

class _CameraPageState extends BasePageStateDelegate<CameraPage, CameraCubit> {
  late ScrollController _imageListScrollController;
  Timer? _animationTimer;
  OverlayEntry? _overlayEntry;

  @override
  void initState() {
    super.initState();
    _imageListScrollController = ScrollController();
    cubit.logEventTrackingOpenCameraPage();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _navigateToCameraCapturePage();
    });
  }

  @override
  void dispose() {
    _imageListScrollController.dispose();
    _animationTimer?.cancel();
    _removeOverlay();
    super.dispose();
    widget.onClose?.call();
  }

  void _showAnimationOverlay() {
    if (!GetIt.instance.get<LocalService>().isShowSwapAnimation()) {
      return;
    }
    _removeOverlay();

    final overlay = Overlay.of(context);

    _overlayEntry = OverlayEntry(
      builder: (context) => Material(
        color: Colors.black.withOpacity(0.5),
        child: Stack(
          children: [
            Positioned(
              top: MediaQuery.of(context).viewPadding.top + 12.h,
              left: 0,
              right: 78.w,
              child: Lottie.asset(
                Assets.videos.swapImage,
                repeat: true,
                width: 168.w,
                height: 168.w,
                fit: BoxFit.contain,
              ),
            ),
          ],
        ),
      ),
    );

    overlay.insert(_overlayEntry!);

    _animationTimer?.cancel();
    _animationTimer = Timer(const Duration(seconds: 3), () {
      GetIt.instance.get<LocalService>().setShowSwapAnimation(false);
      _removeOverlay();
    });
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  @override
  Widget buildPageListeners({required Widget child}) {
    return MultiBlocListener(
      listeners: [
        BlocListener<CameraCubit, CameraState>(
          listenWhen: (previous, current) =>
              previous.selectedImagePaths != current.selectedImagePaths &&
              previous.selectedImagePaths.length <
                  current.selectedImagePaths.length,
          listener: (context, state) {
            if (state.selectedImagePaths.isNotEmpty) {
              _scrollToLastImage();
            }
          },
        ),

        // Handle loading dialog for image merging
        BlocListener<CameraCubit, CameraState>(
          listenWhen: (previous, current) =>
              previous.isMergingImages != current.isMergingImages,
          listener: (context, state) {
            if (state.isMergingImages) {
              CommonDialogs.showLoadingDialog(
                dialogText: ValueNotifier(S.current.processing_image),
              );
            } else if (CommonDialogs.isLoadingDialogOpen) {
              CommonDialogs.closeLoading();
            }
          },
        ),

        // Handle one-shot events
        BlocListener<CameraCubit, CameraState>(
          listenWhen: (previous, current) =>
              previous.oneShotEvent != current.oneShotEvent &&
              current.oneShotEvent != CreateNoteWithCameraOneShotEvent.none,
          listener: (context, state) async {
            _handleOneShotEvent(context, state.oneShotEvent);
            cubit.resetEnumState();
          },
        ),

        // Show animation when new images are added
        BlocListener<CameraCubit, CameraState>(
          listenWhen: (previous, current) =>
              previous.selectedImagePaths != current.selectedImagePaths &&
              current.selectedImagePaths.isNotEmpty &&
              previous.selectedImagePaths.length <
                  current.selectedImagePaths.length,
          listener: (context, state) {
            _showAnimationOverlay();
          },
        ),
      ],
      child: child,
    );
  }

  void _handleOneShotEvent(
      BuildContext context, CreateNoteWithCameraOneShotEvent event) async {
    switch (event) {
      case CreateNoteWithCameraOneShotEvent.mergePdfCompleted:
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => ProgressPage(
              note: cubit.getImageNote(),
              onRetry: () => cubit.onSubmitImages(),
            ),
          ),
        );
        break;

      case CreateNoteWithCameraOneShotEvent.createNoteSuccessfully:
        await _handleNoteCreationSuccess();
        break;

      case CreateNoteWithCameraOneShotEvent.onShowIAPFromCamera:
        _showPurchasePage();
        break;

      case CreateNoteWithCameraOneShotEvent.maxImagesReached:
        CommonDialogs.showToast(S.current.support_for_up_to_10_images);
        break;

      case CreateNoteWithCameraOneShotEvent.mergePdfFailed:
        CommonDialogs.showToast(S.current.cannot_create_pdf_file_from_image);
        break;

      default:
        break;
    }
  }

  Future<void> _handleNoteCreationSuccess() async {
    final noteModel = cubit.getImageNote();
    cubit.resetState();

    widget.onClose?.call();
    widget.onCreateNoteSuccess?.call();

    Navigator.of(context).pop();
    final savedTabs =
        await GetIt.instance.get<LocalService>().loadSelectedItems();
    await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => MyNoteDetailPage(
          noteModel: noteModel,
          isTablet: cubit.appCubit.isTablet,
          from: NoteDetailPageFrom.imageScreen,
          savedTabs: savedTabs,
        ),
      ),
    );
  }

  void _showPurchasePage() {
    Navigator.of(context).pushNamed(
      PurchasePage.routeName,
      arguments: {EventKey.from: PurchasePageFrom.iapCreateNote},
    ).then((didPurchaseSuccess) {
      if (didPurchaseSuccess == true) {
        cubit.onSubmitImages();
      }
    });
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      appBar: AppBarWidget(
        isShowLeftButton: true,
        title: S.current.camera,
        onPressed: _onBackPressed,
      ),
      body: Container(
        margin: context.isTablet
            ? const EdgeInsets.symmetric(horizontal: 16)
            : EdgeInsets.symmetric(horizontal: 16.w),
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AppConstants.kSpacingItem16,
                    _buildImagePickerSection(context),
                    AppConstants.kSpacingItem16,
                    _buildFolderSection(),
                    AppConstants.kSpacingItem16,
                    _buildLanguageSection(),
                    AppConstants.kSpacingItem16,
                    AdvancedWidget(
                      onSummaryStyleChanged: cubit.updateSummaryStyle,
                      onWritingStyleChanged: cubit.updateWritingStyle,
                      onAdditionalInstructionsChanged:
                          cubit.updateAdditionalInstructions,
                      onAdvancedToggled: cubit.setAdvancedEnabled,
                    ),
                  ],
                ),
              ),
            ),
            _buildSubmitButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildFolderSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CommonText(
          S.current.folder,
          textColor: context.colorScheme.mainGray,
          style: TextStyle(
            fontSize: cubit.appCubit.isTablet ? 16 : 14.sp,
            fontWeight: FontWeight.w400,
          ),
        ),
        AppConstants.kSpacingItem8,
        SizedBox(
          width: double.infinity,
          child: FolderDropdownView(
            folders: [
              FolderModel(
                id: 'all_notes',
                folderName: S.current.all_note,
                backendId: '',
              ),
              ...HiveFolderService.getAllFolders()
            ],
            selectedFolder: cubit.selectFolderNotifier,
            onMenuStateChanged: (isShowing) {},
            from: FolderDropdownFrom.camera,
          ),
        ),
      ],
    );
  }

  Widget _buildLanguageSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            CommonText(
              S.current.summary_language,
              textColor: context.colorScheme.mainGray,
              style: TextStyle(
                fontSize: cubit.appCubit.isTablet ? 16 : 14.sp,
                fontWeight: FontWeight.w400,
              ),
            ),
            AppConstants.kSpacingItemW4,
            ElTooltip(
              padding: EdgeInsets.all(8.w),
              content: const LanguageTipsWidget(
                isRecording: false,
                isCheckDocAndText: true,
              ),
              position: ElTooltipPosition.bottomCenter,
              color: context.colorScheme.mainBlue,
              radius: Radius.circular(16.r),
              child: SvgPicture.asset(
                Assets.icons.icCommonInfoTooltip,
                height: cubit.appCubit.isTablet ? 18 : 18.w,
                width: cubit.appCubit.isTablet ? 18 : 18.w,
                colorFilter: ColorFilter.mode(
                  context.colorScheme.mainPrimary,
                  BlendMode.srcIn,
                ),
              ),
            ),
          ],
        ),
        AppConstants.kSpacingItem8,
        SizedBox(
          width: double.infinity,
          child: LanguageDropdownView(
            initialLanguageCode: null,
            onChanged: (lang) => cubit.setCameraLanguage(lang),
            onMenuStateChanged: (isShowing) {},
            useCase: LanguageDropdownUseCase.translateLanguageWithAuto,
            from: LanguageDropdownFrom.camera,
          ),
        ),
      ],
    );
  }

  Widget _buildSubmitButton() {
    return Padding(
      padding: EdgeInsets.only(
        top: 16.h,
        bottom: MediaQuery.of(context).viewInsets.bottom > 0 ? 16.h : 32.h,
      ),
      child: BlocBuilder<CameraCubit, CameraState>(
        buildWhen: (previous, current) =>
            previous.selectedImagePaths != current.selectedImagePaths,
        builder: (context, state) {
          final hasImages = state.selectedImagePaths.isNotEmpty;
          final gradientColors = hasImages
              ? context.colorScheme.mainBlue
              : context.colorScheme.mainNeutral;

          return AppCommonButton(
            width: cubit.appCubit.isTablet ? 200 : 160.w,
            height: cubit.appCubit.isTablet ? 44 : 44.h,
            borderRadius: BorderRadius.circular(24.r),
            backgroundColor: gradientColors,
            textWidget: Text(
              S.current.add_to_notes,
              style: TextStyle(
                fontSize: cubit.appCubit.isTablet ? 18 : 16.sp,
                fontWeight: FontWeight.w500,
                color: hasImages
                    ? context.colorScheme.themeWhite
                    : context.colorScheme.mainPrimary.withOpacity(0.38),
              ),
            ),
            onPressed: hasImages
                ? () {
                    cubit.focusNodeCreateNote.unfocus();
                    cubit.onSubmitImages();
                  }
                : null,
          );
        },
      ),
    );
  }

  Widget _buildImagePickerSection(BuildContext context) {
    return BlocBuilder<CameraCubit, CameraState>(
      buildWhen: (prev, curr) =>
          prev.selectedImagePaths != curr.selectedImagePaths,
      builder: (context, state) {
        final images = state.selectedImagePaths;
        return SizedBox(
          height: context.isTablet ? 60 : 60.h,
          child: Stack(
            children: [
              Row(
                children: [
                  // Camera button
                  GestureDetector(
                    onTap: _navigateToCameraCapturePage,
                    child: Padding(
                      padding: EdgeInsets.only(right: 8.w),
                      child: SvgPicture.asset(
                        context.isDarkMode
                            ? Assets.icons.icTakePictures
                            : Assets.icons.icTakePicturesLightMode,
                        width: context.isTablet ? 60 : 60.w,
                        height: context.isTablet ? 60 : 60.h,
                      ),
                    ),
                  ),

                  // Image list
                  if (images.isNotEmpty)
                    Expanded(
                      child: ReorderableListView.builder(
                        scrollDirection: Axis.horizontal,
                        scrollController: _imageListScrollController,
                        itemCount: images.length,
                        onReorder: _handleImageReorder,
                        proxyDecorator: (child, index, animation) => Material(
                          elevation: 4.0 * animation.value,
                          color: Colors.transparent,
                          child: child,
                        ),
                        itemBuilder: (context, index) {
                          final imgPath = images[index];
                          return Stack(
                            key: ValueKey(imgPath),
                            children: [
                              // Image thumbnail
                              GestureDetector(
                                onTap: () => _openImagePreview(images, index),
                                child: Padding(
                                  padding: EdgeInsets.only(right: 8.w),
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(8.r),
                                    child: Image.file(
                                      File(imgPath),
                                      width: context.isTablet ? 60 : 60.w,
                                      height: context.isTablet ? 60 : 60.h,
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),
                              ),

                              // Delete button
                              Positioned(
                                top: 2,
                                right: 10,
                                child: GestureDetector(
                                  onTap: () => cubit.removeImage(index),
                                  child: SvgPicture.asset(
                                    Assets.icons.icCloseBlack,
                                    width: context.isTablet ? 20 : 12.w,
                                    height: context.isTablet ? 20 : 12.h,
                                  ),
                                ),
                              ),

                              // Image counter
                              Positioned(
                                bottom: 0,
                                left: 0,
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 6,
                                    vertical: 2,
                                  ),
                                  decoration: BoxDecoration(
                                    color:
                                        AppColors.primaryBlue.withOpacity(0.3),
                                    borderRadius: BorderRadius.only(
                                      bottomLeft: Radius.circular(8.r),
                                    ),
                                    boxShadow: const [
                                      BoxShadow(
                                        color: AppColors.primaryBlue,
                                        blurRadius: 30,
                                        spreadRadius: -10,
                                        blurStyle: BlurStyle.inner,
                                      ),
                                    ],
                                  ),
                                  child: Text(
                                    '${index + 1}',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: context.isTablet ? 12 : 10.sp,
                                      fontWeight: FontWeight.w700,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          );
                        },
                      ),
                    ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _navigateToCameraCapturePage() async {
    try {
      final result = await Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => CameraCapturePage(
            onCreateNoteSuccess: widget.onCreateNoteSuccess,
            hasImagesInParent: cubit.state.selectedImagePaths.isNotEmpty,
            existingImagesCount: cubit.state.selectedImagePaths.length,
          ),
        ),
      );

      if (result is Map && result['result'] == true && mounted) {
        final List<String> imagePaths = result['imagePaths'] ?? [];
        if (imagePaths.isNotEmpty) {
          cubit.updateImagePaths(imagePaths);
          setState(() {});

          WidgetsBinding.instance.addPostFrameCallback((_) {
            _scrollToLastImage();
          });
        }
      }
    } catch (e) {
      debugPrint('Error in _navigateToCameraCapturePage: $e');
    }
  }

  void _scrollToLastImage() {
    if (!_imageListScrollController.hasClients) return;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _imageListScrollController.animateTo(
        _imageListScrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    });
  }

  void _handleImageReorder(int oldIndex, int newIndex) {
    if (oldIndex < newIndex) {
      newIndex -= 1;
    }
    cubit.swapImages(oldIndex, newIndex);
  }

  void _openImagePreview(List<String> images, int index) {
    cubit.logEventTrackingPreviewImage();
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ImagePreviewGallery(
          imagePaths: images,
          initialIndex: index,
          onSwapImages: cubit.swapImages,
          onDeleteImage: cubit.removeImage,
          onRefresh: () => setState(() {}),
        ),
      ),
    );
  }

  void _onBackPressed() {
    if (cubit.hasUnsavedChanges) {
      showNewCupertinoDialog(
        context: context,
        title: S.current.discard_changes,
        message: S.current.content_discard_changes_image,
        image: Assets.icons.icDiscardChanges,
        cancelButton: S.current.cancel,
        confirmButton: S.current.discard,
        onCancel: () {},
        onConfirm: () {
          cubit.discardChanges();
          _exitPage();
        },
      );
    } else {
      _exitPage();
    }
  }

  void _exitPage() {
    cubit.logEventTrackingCameraBack();
    Navigator.of(context).pop();
  }
}
