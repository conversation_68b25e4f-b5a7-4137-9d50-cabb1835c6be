// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a fr locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'fr';

  static String m0(date) => "Votre essai expirera dans ${date} jours.";

  static String m1(images) => "${images} photos ont été téléchargées";

  static String m2(price, date) =>
      "Votre prochaine facture de ${price} sera le ${date}.";

  static String m3(uid) => "uid ${uid} copié dans le presse-papiers !";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "Regenerate": MessageLookupByLibrary.simpleMessage("Régénérer"),
    "a_to_z": MessageLookupByLibrary.simpleMessage("A à Z"),
    "about_us": MessageLookupByLibrary.simpleMessage("À propos"),
    "access_notex_web": MessageLookupByLibrary.simpleMessage(
      "Accès à NoteX web",
    ),
    "account": MessageLookupByLibrary.simpleMessage("Compte"),
    "account_basic": MessageLookupByLibrary.simpleMessage("Basique"),
    "account_content_basic": MessageLookupByLibrary.simpleMessage(
      "Expérience IA limitée",
    ),
    "account_content_pro": MessageLookupByLibrary.simpleMessage(
      "Débloquer l\'expérience IA illimitée",
    ),
    "account_lifetime": MessageLookupByLibrary.simpleMessage("À vie"),
    "achieve_more": MessageLookupByLibrary.simpleMessage("ACCOMPLIR PLUS"),
    "action_items": MessageLookupByLibrary.simpleMessage("Tâches"),
    "actionable_intelligence": MessageLookupByLibrary.simpleMessage(
      "en actions",
    ),
    "active_description": MessageLookupByLibrary.simpleMessage(
      "Aucune description active trouvée.",
    ),
    "active_recall": MessageLookupByLibrary.simpleMessage("rappel actif"),
    "add_folder": MessageLookupByLibrary.simpleMessage("Déplacer vers dossier"),
    "add_note": MessageLookupByLibrary.simpleMessage("Ajouter une note"),
    "add_password": MessageLookupByLibrary.simpleMessage(
      "Ajouter un mot de passe",
    ),
    "add_password_to_public": MessageLookupByLibrary.simpleMessage(
      "Ajouter un mot de passe au lien public",
    ),
    "add_to": MessageLookupByLibrary.simpleMessage("Déplacer vers"),
    "add_to_notes": MessageLookupByLibrary.simpleMessage("Ajouter aux notes"),
    "additional_ins": MessageLookupByLibrary.simpleMessage(
      "Instructions Supplémentaires (opt.)",
    ),
    "advance_mode": MessageLookupByLibrary.simpleMessage("Mode avancé"),
    "advanced": MessageLookupByLibrary.simpleMessage("Avancé"),
    "afternoon_content": MessageLookupByLibrary.simpleMessage(
      "Petites notes, grand impact",
    ),
    "afternoon_content_1": MessageLookupByLibrary.simpleMessage(
      "Pensées capturées, esprit libre",
    ),
    "afternoon_content_3": MessageLookupByLibrary.simpleMessage(
      "De l\'ordre dans le chaos",
    ),
    "afternoon_content_4": MessageLookupByLibrary.simpleMessage(
      "Vos idées, organisées",
    ),
    "afternoon_content_5": MessageLookupByLibrary.simpleMessage(
      "Clarté en progression",
    ),
    "afternoon_content_6": MessageLookupByLibrary.simpleMessage(
      "Gardez ce qui compte",
    ),
    "ai_audio_transcription_per_day": MessageLookupByLibrary.simpleMessage(
      "3 AI Audio Transcription par jour *",
    ),
    "ai_chat": MessageLookupByLibrary.simpleMessage("Nova IA"),
    "ai_chat_assistant": MessageLookupByLibrary.simpleMessage("Assistant IA"),
    "ai_chat_with_notes": MessageLookupByLibrary.simpleMessage(
      "Chat IA avec notes",
    ),
    "ai_insight": MessageLookupByLibrary.simpleMessage("Analyse IA"),
    "ai_learning": MessageLookupByLibrary.simpleMessage("Apprentissage IA"),
    "ai_learning_companion": MessageLookupByLibrary.simpleMessage(
      "Je suis Nova IA de NoteX",
    ),
    "ai_note_create": MessageLookupByLibrary.simpleMessage(
      "Création de notes IA",
    ),
    "ai_note_creation": MessageLookupByLibrary.simpleMessage(
      "Création notes IA",
    ),
    "ai_note_from": MessageLookupByLibrary.simpleMessage(
      "Notes IA à partir d\'Audio",
    ),
    "ai_notes_10": MessageLookupByLibrary.simpleMessage(
      "Notes IA illimitées depuis YouTube et Documents",
    ),
    "ai_notes_3": MessageLookupByLibrary.simpleMessage(
      "3 notes IA par jour à partir d\'enregistrements et fichiers audio (jusqu\'à 60 min par fichier)",
    ),
    "ai_notes_from": MessageLookupByLibrary.simpleMessage(
      "Notes IA depuis \nYouTube, Web, Docs",
    ),
    "ai_short_1": MessageLookupByLibrary.simpleMessage(
      "3 générations de courtes vidéos IA par jour",
    ),
    "ai_short_3": MessageLookupByLibrary.simpleMessage(
      "5 Courtes vidéos IA par jour (bêta)",
    ),
    "ai_short_video": MessageLookupByLibrary.simpleMessage("Vidéos courtes IA"),
    "ai_study_practice": MessageLookupByLibrary.simpleMessage(
      "Exercices d\'étude IA",
    ),
    "ai_study_tools": MessageLookupByLibrary.simpleMessage(
      "Outils d\'étude IA",
    ),
    "ai_summarize": MessageLookupByLibrary.simpleMessage("Résumé IA"),
    "ai_transcription": MessageLookupByLibrary.simpleMessage(
      "Transcription IA",
    ),
    "ai_workflow": MessageLookupByLibrary.simpleMessage("Flux IA"),
    "all": MessageLookupByLibrary.simpleMessage("Tout"),
    "all_note": MessageLookupByLibrary.simpleMessage("Toutes notes"),
    "all_note_in_folder": MessageLookupByLibrary.simpleMessage(
      "Êtes-vous sûr de vouloir supprimer ce dossier ?",
    ),
    "all_tabs": MessageLookupByLibrary.simpleMessage("Tous les onglets"),
    "allow": MessageLookupByLibrary.simpleMessage("Autoriser"),
    "almost_done": MessageLookupByLibrary.simpleMessage("Presque fini"),
    "and": MessageLookupByLibrary.simpleMessage("et"),
    "answer": MessageLookupByLibrary.simpleMessage("Réponse"),
    "anyone_with_link": MessageLookupByLibrary.simpleMessage(
      "Toute personne avec le lien peut voir",
    ),
    "app_feedback": MessageLookupByLibrary.simpleMessage(
      "Feedback pour l\'application NoteX",
    ),
    "app_store": MessageLookupByLibrary.simpleMessage("avis sur l\'App Store"),
    "appearance": MessageLookupByLibrary.simpleMessage("Apparence"),
    "appreciate_cooperation": MessageLookupByLibrary.simpleMessage(
      "Ces informations aideront notre équipe de support à identifier et résoudre rapidement votre problème. Nous apprécions votre coopération pour améliorer NoteX pour tous.",
    ),
    "appreciate_cooperation2": MessageLookupByLibrary.simpleMessage(
      "Cela nous aide à enquêter et résoudre votre problème plus efficacement.",
    ),
    "appreciate_cooperation3": MessageLookupByLibrary.simpleMessage(
      "Merci d\'utiliser et de faire confiance à NoteX AI !",
    ),
    "are_you_sure": MessageLookupByLibrary.simpleMessage("Offre unique"),
    "ask_anything": MessageLookupByLibrary.simpleMessage(
      "Hésitez pas à poser des questions...",
    ),
    "assist_faster": MessageLookupByLibrary.simpleMessage(
      "Pour nous aider à vous assister plus rapidement :",
    ),
    "assistant": MessageLookupByLibrary.simpleMessage("assistant"),
    "at_your_pace": MessageLookupByLibrary.simpleMessage("un A+"),
    "audio": MessageLookupByLibrary.simpleMessage("Audio"),
    "audio_file": MessageLookupByLibrary.simpleMessage("Fichier Audio"),
    "audio_is_temporary": MessageLookupByLibrary.simpleMessage(
      "*L\'audio est temporaire - Enregistrez avant de fermer",
    ),
    "audio_length_err": MessageLookupByLibrary.simpleMessage(
      "Le fichier audio dépasse la longueur maximale. Veuillez télécharger un fichier plus court.",
    ),
    "audio_length_limit": MessageLookupByLibrary.simpleMessage(
      "Limite de durée de l\'audio",
    ),
    "audio_process_err": MessageLookupByLibrary.simpleMessage(
      "Impossible de traiter le fichier audio. Veuillez réessayer avec un autre fichier.",
    ),
    "audio_recording_ai_notes_daily": MessageLookupByLibrary.simpleMessage(
      "3 Notes IA Audio et Enregistrement par jour*",
    ),
    "audio_to_ai_note": MessageLookupByLibrary.simpleMessage(
      "Audio vers notes IA",
    ),
    "audio_upload_note": MessageLookupByLibrary.simpleMessage(
      "Téléchargement audio",
    ),
    "auto": MessageLookupByLibrary.simpleMessage("Auto"),
    "auto_detect": MessageLookupByLibrary.simpleMessage(
      "Détection automatique",
    ),
    "auto_generate_slides": MessageLookupByLibrary.simpleMessage(
      "Générez instantanément des diapositives captivantes",
    ),
    "auto_renew_after_trial": MessageLookupByLibrary.simpleMessage(
      "Auto-renouvelé • Annulez à tout moment",
    ),
    "auto_renewable_after_trial": MessageLookupByLibrary.simpleMessage(
      "Renouvelable automatiquement après l\'essai. Annulez à tout moment",
    ),
    "auto_renewal": MessageLookupByLibrary.simpleMessage(
      "Renouvellement auto, annulez à tout moment",
    ),
    "available_credits": MessageLookupByLibrary.simpleMessage(
      "Crédits disponibles",
    ),
    "available_transcript": MessageLookupByLibrary.simpleMessage(
      "La transcription sera disponible une fois la note créée avec succès !",
    ),
    "back_content": MessageLookupByLibrary.simpleMessage(" points"),
    "background_style": MessageLookupByLibrary.simpleMessage(
      "Style d\'arrière-plan",
    ),
    "balanced": MessageLookupByLibrary.simpleMessage("Équilibré"),
    "balanced_description": MessageLookupByLibrary.simpleMessage(
      "Idées principales avec contexte",
    ),
    "basic": MessageLookupByLibrary.simpleMessage("Plan Basique"),
    "basic_features": MessageLookupByLibrary.simpleMessage(
      "Fonctionnalités IA basiques",
    ),
    "beta": MessageLookupByLibrary.simpleMessage("Bêta"),
    "between_concepts": MessageLookupByLibrary.simpleMessage(
      "Reliez les points entre les concepts",
    ),
    "black_friday_sale": MessageLookupByLibrary.simpleMessage(
      "Promotion Noël !",
    ),
    "blurred_output_image": MessageLookupByLibrary.simpleMessage(
      "Échec de la génération du style ! Choisissez un autre style ou une autre image !",
    ),
    "body_error_document_upload": MessageLookupByLibrary.simpleMessage(
      "Un problème est survenu lors du traitement de votre document. Veuillez retourner à l\'application et réessayer.",
    ),
    "body_error_note_document": MessageLookupByLibrary.simpleMessage(
      "Un problème est survenu lors du traitement de votre document. Veuillez retourner à l\'application et réessayer.",
    ),
    "body_error_note_recording": MessageLookupByLibrary.simpleMessage(
      "Un problème est survenu lors du traitement de votre enregistrement. Veuillez retourner à l\'application et réessayer.",
    ),
    "body_error_note_upload": MessageLookupByLibrary.simpleMessage(
      "Un problème est survenu lors du traitement de votre fichier audio. Veuillez retourner à l\'application et réessayer.",
    ),
    "body_error_note_web": MessageLookupByLibrary.simpleMessage(
      "Un problème est survenu lors du traitement de votre lien web. Veuillez retourner dans l\'application et réessayer.",
    ),
    "body_error_note_youtube": MessageLookupByLibrary.simpleMessage(
      "Un problème est survenu lors du traitement de votre lien YouTube. Veuillez retourner dans l\'application et réessayer.",
    ),
    "body_success_note": MessageLookupByLibrary.simpleMessage(
      "Votre note IA est prête à être consultée.",
    ),
    "bonus_credits_for_new_referred_friends_only":
        MessageLookupByLibrary.simpleMessage(
          "Crédits bonus pour nouveaux amis référés",
        ),
    "boost_comprehension": MessageLookupByLibrary.simpleMessage(
      "Renforcez la compréhension et la rétention",
    ),
    "boost_comprehension2": MessageLookupByLibrary.simpleMessage(
      "Boost compréhension",
    ),
    "boost_flashcards_quizzes": MessageLookupByLibrary.simpleMessage(
      "Renforcez votre compréhension avec des cartes et quizz IA",
    ),
    "boost_knowledge": MessageLookupByLibrary.simpleMessage(
      "Reliez les points",
    ),
    "boost_knowledge_retention": MessageLookupByLibrary.simpleMessage(
      "Reliez les points entre",
    ),
    "both_you_friends_receive_usage_credits":
        MessageLookupByLibrary.simpleMessage(
          "Vous et vos amis recevrez des crédits d\'utilisation.",
        ),
    "brief_service_disruption": MessageLookupByLibrary.simpleMessage(
      "Tâche introuvable ou expirée. Veuillez réessayer. Obtenez les mises à jour de l\'état en temps réel sur Discord !",
    ),
    "business_uses": MessageLookupByLibrary.simpleMessage(
      "Utilisations professionnelles",
    ),
    "button_below": MessageLookupByLibrary.simpleMessage(
      "Appuyez sur le bouton ci-dessous ou choisissez un type de contenu à saisir pour commencer",
    ),
    "buy_one_forever": MessageLookupByLibrary.simpleMessage(
      "Achetez une fois. Productivité maximale pour toujours.",
    ),
    "by_subscribing": MessageLookupByLibrary.simpleMessage(
      "En vous abonnant, vous acceptez les",
    ),
    "by_taping_continue": MessageLookupByLibrary.simpleMessage(
      "En continuant, vous acceptez",
    ),
    "by_tapping_started": MessageLookupByLibrary.simpleMessage(
      "En appuyant sur « Commencer », vous acceptez nos",
    ),
    "camera": MessageLookupByLibrary.simpleMessage("Appareil photo"),
    "camera_access": MessageLookupByLibrary.simpleMessage(
      "\"NoteX\" Souhaite Accéder à l\'Appareil Photo",
    ),
    "camera_permission": MessageLookupByLibrary.simpleMessage(
      "Accès à la caméra requis",
    ),
    "camera_permission_denied_details": MessageLookupByLibrary.simpleMessage(
      "Accès à la caméra requis pour sélectionner des images. Veuillez accorder le permis dans les paramètres.",
    ),
    "can_improve": MessageLookupByLibrary.simpleMessage(
      "Que pouvons-nous améliorer ?",
    ),
    "cancel": MessageLookupByLibrary.simpleMessage("Annuler"),
    "cannot_create_pdf_file_from_image": MessageLookupByLibrary.simpleMessage(
      "Impossible de créer un fichier PDF à partir de l\'image",
    ),
    "cannot_extract_text_from_pdf": MessageLookupByLibrary.simpleMessage(
      "Impossible de lire le document. Aucun texte n\'a pu être extrait. Cela arrive souvent avec des documents scannés ou des PDF contenant uniquement des images.",
    ),
    "card": MessageLookupByLibrary.simpleMessage("Carte"),
    "card_count": MessageLookupByLibrary.simpleMessage("Nombre de cartes"),
    "card_difficulty": MessageLookupByLibrary.simpleMessage(
      "Difficulté des cartes",
    ),
    "change": MessageLookupByLibrary.simpleMessage("Modifier"),
    "change_plan": MessageLookupByLibrary.simpleMessage("Changer de forfait"),
    "chaos_into_clarity": MessageLookupByLibrary.simpleMessage(
      "du chaos à la clarté",
    ),
    "characters": MessageLookupByLibrary.simpleMessage("caractères"),
    "chat_empty": MessageLookupByLibrary.simpleMessage("Chat vide"),
    "chat_topic_temporary_stored": MessageLookupByLibrary.simpleMessage(
      "Session temporaire, utilisez \"Sauvegarder Chat\" pour conserver",
    ),
    "check_if_you": MessageLookupByLibrary.simpleMessage(
      "Vérifiez que vous êtes connecté au bon compte Google",
    ),
    "check_update": MessageLookupByLibrary.simpleMessage(
      "Nouvelle version disponible",
    ),
    "child_detected": MessageLookupByLibrary.simpleMessage(
      "Enfant détecté. Téléchargez une autre image.",
    ),
    "choose_your_note": MessageLookupByLibrary.simpleMessage(
      "Choisissez votre NoteX",
    ),
    "choose_your_note_experience": MessageLookupByLibrary.simpleMessage(
      "Choisissez votre expérience NoteX",
    ),
    "click_create_podcast": MessageLookupByLibrary.simpleMessage(
      "Cliquez sur \'Créer un podcast\' pour transformer votre note en audio captivant",
    ),
    "click_create_short": MessageLookupByLibrary.simpleMessage(
      "Cliquez sur \'Créer un Short\' pour transformer votre note en shorts captivants",
    ),
    "click_create_slide": MessageLookupByLibrary.simpleMessage(
      "Cliquez sur \'Créer un diaporama\' pour visualiser votre note comme une présentation",
    ),
    "click_start_flashcard": MessageLookupByLibrary.simpleMessage(
      "Cliquez sur \'Créer des fiches\' pour générer des séries de fiches basées sur la transcription. Vous pouvez créer plusieurs séries.",
    ),
    "click_start_mindmap": MessageLookupByLibrary.simpleMessage(
      "Cliquez sur \'Créer une carte mentale\' pour générer une carte mentale basée sur la transcription",
    ),
    "click_start_quiz": MessageLookupByLibrary.simpleMessage(
      "Cliquez sur \'Créer des quiz\' pour générer des séries de questions basées sur la transcription. Vous pouvez créer plusieurs séries.",
    ),
    "click_to_flip": MessageLookupByLibrary.simpleMessage(
      "Cliquer pour retourner",
    ),
    "coming_soon": MessageLookupByLibrary.simpleMessage("Bientôt disponible"),
    "community": MessageLookupByLibrary.simpleMessage("Communauté"),
    "community_feedback": MessageLookupByLibrary.simpleMessage(
      "Communauté et retour d\'expérience",
    ),
    "comprehensive": MessageLookupByLibrary.simpleMessage("Complet"),
    "comprehensive_description": MessageLookupByLibrary.simpleMessage(
      "Couverture détaillée avec points d\'appui",
    ),
    "congratulations": MessageLookupByLibrary.simpleMessage("Félicitations !"),
    "connect_friends": MessageLookupByLibrary.simpleMessage(
      "Importez facilement les liens de notes partagées par vos amis",
    ),
    "connection_fail": MessageLookupByLibrary.simpleMessage(
      "Échec de la connexion !",
    ),
    "connection_timeout": MessageLookupByLibrary.simpleMessage(
      "Délai de connexion dépassé. Vérifiez votre connexion internet et réessayez.",
    ),
    "contact_support": MessageLookupByLibrary.simpleMessage("Contactez-nous"),
    "content_account_trial": m0,
    "content_button_flashcard": MessageLookupByLibrary.simpleMessage(
      "Créer des fiches",
    ),
    "content_button_mindmap": MessageLookupByLibrary.simpleMessage(
      "Créer une carte mentale",
    ),
    "content_button_quiz": MessageLookupByLibrary.simpleMessage(
      "Créer des quiz",
    ),
    "content_button_summary": MessageLookupByLibrary.simpleMessage(
      "Générer un résumé",
    ),
    "content_camera_access": MessageLookupByLibrary.simpleMessage(
      "NoteX a besoin d\'accéder à votre appareil photo pour capturer, reconnaître et numériser des textes à partir d\'images",
    ),
    "content_delete_note": MessageLookupByLibrary.simpleMessage(
      "Vous ne pourrez pas la récupérer après.",
    ),
    "content_delete_note_detail": MessageLookupByLibrary.simpleMessage(
      "Êtes-vous sûr de vouloir supprimer cette note ?",
    ),
    "content_delete_reminder": MessageLookupByLibrary.simpleMessage(
      "Êtes-vous sûr de vouloir supprimer ce rappel?",
    ),
    "content_discard_changes": MessageLookupByLibrary.simpleMessage(
      "Quitter arrêtera l\'enregistrement et annulera les modifications.",
    ),
    "content_discard_changes_image": MessageLookupByLibrary.simpleMessage(
      "La fermeture supprimera les photos que vous avez prises",
    ),
    "content_discard_changes_note": MessageLookupByLibrary.simpleMessage(
      "Cette action annulera tous les changements de façon irréversible.",
    ),
    "content_discard_changes_reminder": MessageLookupByLibrary.simpleMessage(
      "Quitter fermera la notification de rappel et annulera toutes les modifications.",
    ),
    "content_empty_flashcard": MessageLookupByLibrary.simpleMessage(
      "Un résumé automatique apparaîtra ici après la fin de la réunion.",
    ),
    "content_empty_quiz": MessageLookupByLibrary.simpleMessage(
      "Un résumé automatique apparaîtra ici après la fin de la réunion.",
    ),
    "content_hour": MessageLookupByLibrary.simpleMessage(
      "Heures de contenu en",
    ),
    "content_hour_insight": MessageLookupByLibrary.simpleMessage(
      "Heures de contenu en aperçu",
    ),
    "content_minute_left": MessageLookupByLibrary.simpleMessage(
      "Vos enregistrements seront sauvegardés localement si le temps restant gratuit est dépassé cette semaine. Supprimez les limites avec Pro.",
    ),
    "content_payment_successfully": MessageLookupByLibrary.simpleMessage(
      "Merci pour votre achat. Votre transaction a été effectuée avec succès.",
    ),
    "content_quarter_01": MessageLookupByLibrary.simpleMessage(
      "Notes IA illimitées à partir d\'enregistrements, de fichiers téléchargés, de liens YouTube.",
    ),
    "content_quarter_02": MessageLookupByLibrary.simpleMessage(
      "Chat IA illimité, Carte mentale, Flashcards, Quiz, Partage de notes.",
    ),
    "content_save_changes": MessageLookupByLibrary.simpleMessage(
      "Cette action enregistrera les modifications de manière permanente.",
    ),
    "continue_3_day": MessageLookupByLibrary.simpleMessage(
      "Continuer l\'essai gratuit de 3 jours",
    ),
    "continue_button": MessageLookupByLibrary.simpleMessage("Continuer"),
    "continue_with_apple": MessageLookupByLibrary.simpleMessage(
      "Continuer avec Apple",
    ),
    "continue_with_email": MessageLookupByLibrary.simpleMessage(
      "Continuer avec l\'e-mail",
    ),
    "continue_with_google": MessageLookupByLibrary.simpleMessage(
      "Continuer avec Google",
    ),
    "copied_to_clipboard": MessageLookupByLibrary.simpleMessage(
      "Copié dans le presse-papiers",
    ),
    "copy": MessageLookupByLibrary.simpleMessage("Copier"),
    "copy_your_referral_code": MessageLookupByLibrary.simpleMessage(
      "Copiez votre code de parrainage.",
    ),
    "correct": MessageLookupByLibrary.simpleMessage("Correct"),
    "craft_visual_from_every_note": MessageLookupByLibrary.simpleMessage(
      "Transformez vos notes en diapositives",
    ),
    "craft_visual_stories": MessageLookupByLibrary.simpleMessage(
      "Transformez vos notes",
    ),
    "create": MessageLookupByLibrary.simpleMessage("Créer"),
    "create_folder": MessageLookupByLibrary.simpleMessage("Créer un dossier"),
    "create_lecture": MessageLookupByLibrary.simpleMessage(
      "Créer des résumés concis de vos cours",
    ),
    "create_new_folder": MessageLookupByLibrary.simpleMessage(
      "Créer un nouveau dossier",
    ),
    "create_note_successfully": MessageLookupByLibrary.simpleMessage(
      "Note créée avec succès !",
    ),
    "create_notes": MessageLookupByLibrary.simpleMessage(
      "Création des notes IA..",
    ),
    "create_podcast": MessageLookupByLibrary.simpleMessage("Créer un podcast"),
    "create_reminder": MessageLookupByLibrary.simpleMessage("Créer un rappel"),
    "create_select_a_language": MessageLookupByLibrary.simpleMessage(
      "Sélectionner une langue",
    ),
    "create_short": MessageLookupByLibrary.simpleMessage("Créer un Short"),
    "create_shorts": MessageLookupByLibrary.simpleMessage("Créer des Shorts"),
    "create_slide": MessageLookupByLibrary.simpleMessage("Créer un diaporama"),
    "creating_note": MessageLookupByLibrary.simpleMessage(
      "Création de la note ...",
    ),
    "creating_quiz": MessageLookupByLibrary.simpleMessage(
      "Création des questions du quiz",
    ),
    "credit": MessageLookupByLibrary.simpleMessage("Crédit"),
    "credits": MessageLookupByLibrary.simpleMessage("Crédits"),
    "credits_can_be_used_to_create_notes_and_access_the_features_within_them":
        MessageLookupByLibrary.simpleMessage(
          "Les crédits peuvent être utilisés pour créer des notes et accéder aux fonctionnalités qu\'elles contiennent. Si votre abonnement expire, vous pouvez utiliser des crédits pour continuer à effectuer des actions.",
        ),
    "credits_earned": MessageLookupByLibrary.simpleMessage("Crédits gagnés"),
    "credits_premium_features": MessageLookupByLibrary.simpleMessage(
      "crédits et fonctionnalités premium supplémentaires.",
    ),
    "credits_used": MessageLookupByLibrary.simpleMessage("Crédits utilisés"),
    "current_plan": MessageLookupByLibrary.simpleMessage("Forfait actuel"),
    "customize_note_tabs": MessageLookupByLibrary.simpleMessage(
      "Personnaliser les onglets de notes",
    ),
    "customize_your_note_view": MessageLookupByLibrary.simpleMessage(
      "Personnalisez l\'affichage de vos notes",
    ),
    "daily_10": MessageLookupByLibrary.simpleMessage("10 par jour"),
    "daily_3": MessageLookupByLibrary.simpleMessage("3 par jour"),
    "daily_5": MessageLookupByLibrary.simpleMessage("5 par jour"),
    "daily_rewards_limit_reached": MessageLookupByLibrary.simpleMessage(
      "Limite des récompenses journalières atteinte. Réessayez demain !",
    ),
    "daily_shorts_limit_reached": MessageLookupByLibrary.simpleMessage(
      "Limite quotidienne de Shorts atteinte (Bêta - Accès anticipé)",
    ),
    "daily_shorts_limit_reached_detail": MessageLookupByLibrary.simpleMessage(
      "Vous avez utilisé toutes vos générations de Shorts pour aujourd\'hui. Cette fonctionnalité bêta a des limites quotidiennes pour assurer un service stable. \nRevenez demain pour créer plus de vidéos courtes IA !",
    ),
    "daily_slideshow_limit_reached": MessageLookupByLibrary.simpleMessage(
      "Limite de diapositives quotidiennes atteint",
    ),
    "daily_slideshow_limit_reached_detail": MessageLookupByLibrary.simpleMessage(
      "Vous avez utilisé toutes les générations de diapositives pour aujourd\'hui. Cette fonctionnalité bêta a des limites quotidiennes pour garantir un rendement stable. Revenez demain pour créer plus de diaporamas alimentés par l\'IA !",
    ),
    "dark": MessageLookupByLibrary.simpleMessage("Sombre"),
    "data": MessageLookupByLibrary.simpleMessage("données"),
    "day_free_trial_access_all_features": MessageLookupByLibrary.simpleMessage(
      "7-jours d\'essai gratuit pour accéder à toutes les fonctionnalités, puis juste ",
    ),
    "days": MessageLookupByLibrary.simpleMessage("Jours"),
    "db_err": MessageLookupByLibrary.simpleMessage(
      "Erreur de base de données. Veuillez réessayer plus tard.",
    ),
    "deals_left_at_this_price": MessageLookupByLibrary.simpleMessage(
      "offres à vie à ce prix",
    ),
    "decline_free_trial": MessageLookupByLibrary.simpleMessage(
      "Refuser l\'essai gratuit",
    ),
    "default_error": MessageLookupByLibrary.simpleMessage(
      "Un problème est survenu ! Veuillez réessayer !",
    ),
    "delete": MessageLookupByLibrary.simpleMessage("Supprimer"),
    "delete_account": MessageLookupByLibrary.simpleMessage(
      "Supprimer le compte",
    ),
    "delete_account_detail": MessageLookupByLibrary.simpleMessage(
      "Cette action est irréversible. Supprimer votre compte supprimera définitivement : Toutes vos notes et enregistrements",
    ),
    "delete_all_note": MessageLookupByLibrary.simpleMessage(
      "Supprimer toutes les notes du dossier",
    ),
    "delete_folder": MessageLookupByLibrary.simpleMessage(
      "Supprimer le dossier",
    ),
    "delete_note": MessageLookupByLibrary.simpleMessage(
      "Supprimer cette note ?",
    ),
    "delete_note_item": MessageLookupByLibrary.simpleMessage(
      "Supprimer la note",
    ),
    "delete_recording": MessageLookupByLibrary.simpleMessage(
      "Supprimer l\'enregistrement",
    ),
    "delete_recording_confirmation": MessageLookupByLibrary.simpleMessage(
      "Êtes-vous sûr de vouloir supprimer cette enregistrement",
    ),
    "delete_recording_setting_confirmation": MessageLookupByLibrary.simpleMessage(
      "Le fichier audio sera supprimé définitivement de votre appareil. Cette action ne peut pas être annulée.",
    ),
    "delete_reminder": MessageLookupByLibrary.simpleMessage(
      "Supprimer le rappel ?",
    ),
    "delete_success": MessageLookupByLibrary.simpleMessage(
      "La note a été supprimée avec succès.",
    ),
    "delete_this_folder": MessageLookupByLibrary.simpleMessage(
      "Supprimer ce dossier ?",
    ),
    "delete_this_item": MessageLookupByLibrary.simpleMessage(
      "Supprimer cet élément ?",
    ),
    "deselect": MessageLookupByLibrary.simpleMessage("Désélectionner"),
    "detail_unlimited_ai_summaries": MessageLookupByLibrary.simpleMessage(
      "Notes IA illimitées pour enregistrements, fichiers audio, documents et vidéos YouTube",
    ),
    "developing_quizzes": MessageLookupByLibrary.simpleMessage(
      "Création des quiz..",
    ),
    "discard": MessageLookupByLibrary.simpleMessage("Annuler"),
    "discard_changes": MessageLookupByLibrary.simpleMessage(
      "Annuler les modifications ?",
    ),
    "dissatisfied": MessageLookupByLibrary.simpleMessage(
      "Merci pour votre retour. Vos commentaires nous aident à améliorer nos résultats et nous travaillerons à rendre votre expérience meilleure la prochaine fois. Merci beaucoup !",
    ),
    "doc": MessageLookupByLibrary.simpleMessage("Document"),
    "document": MessageLookupByLibrary.simpleMessage(
      "Télécharger un document (bientôt)",
    ),
    "document_available": MessageLookupByLibrary.simpleMessage(
      "Le document sera disponible après la création de la note avec succès!",
    ),
    "document_exceed_limit": MessageLookupByLibrary.simpleMessage(
      "Fichier dépasse 20MB. Choisir plus petit",
    ),
    "document_limit": MessageLookupByLibrary.simpleMessage(
      "Limite de téléchargement de documents",
    ),
    "document_limit_message": MessageLookupByLibrary.simpleMessage(
      "Les utilisateurs gratuits peuvent résumer 1 document par jour.",
    ),
    "document_note": MessageLookupByLibrary.simpleMessage("Note de document"),
    "document_tab": MessageLookupByLibrary.simpleMessage("Document"),
    "document_to_ai_note": MessageLookupByLibrary.simpleMessage(
      "Document vers notes IA",
    ),
    "document_type": MessageLookupByLibrary.simpleMessage(
      "Types de fichiers pris en charge : .pdf, .doc, .docx, .txt, .md",
    ),
    "document_upload_note": MessageLookupByLibrary.simpleMessage(
      "Téléchargement de document",
    ),
    "document_webview_loading_message": MessageLookupByLibrary.simpleMessage(
      "Chargement du contenu du document...",
    ),
    "done_button_label": MessageLookupByLibrary.simpleMessage("Fini"),
    "donotallow": MessageLookupByLibrary.simpleMessage("Ne pas autoriser"),
    "double_the_benefits": MessageLookupByLibrary.simpleMessage(
      "Doublez les avantages !",
    ),
    "download_audio_file": MessageLookupByLibrary.simpleMessage(
      "Télécharger l\'audio",
    ),
    "download_sucess": MessageLookupByLibrary.simpleMessage(
      "Téléchargement réussi",
    ),
    "duration": MessageLookupByLibrary.simpleMessage("Durée"),
    "each_ai_note_generation_uses_1_credit":
        MessageLookupByLibrary.simpleMessage(
          "Chaque génération de note IA utilise 1 crédit",
        ),
    "each_referral_earns": MessageLookupByLibrary.simpleMessage(
      "Chaque référence gagne",
    ),
    "early_access": MessageLookupByLibrary.simpleMessage(
      "Accès anticipé aux \nfonctionnalités futures",
    ),
    "early_supporters_exclusive_offer": MessageLookupByLibrary.simpleMessage(
      "Offre exclusive pour les premiers supporters",
    ),
    "easily_import_shared_note_link": MessageLookupByLibrary.simpleMessage(
      "Importez facilement des liens de notes partagées par vos amis",
    ),
    "easy": MessageLookupByLibrary.simpleMessage("Facile"),
    "edit": MessageLookupByLibrary.simpleMessage("Modifier"),
    "edit_folder": MessageLookupByLibrary.simpleMessage("Modifier le dossier"),
    "edit_folder_name": MessageLookupByLibrary.simpleMessage(
      "Saisissez le nom du dossier",
    ),
    "edit_name": MessageLookupByLibrary.simpleMessage("Modifier le nom"),
    "edit_note": MessageLookupByLibrary.simpleMessage("Modifier la note"),
    "edit_notes": MessageLookupByLibrary.simpleMessage("Modifier la note"),
    "edit_reminder": MessageLookupByLibrary.simpleMessage("Modifier le rappel"),
    "edit_transcript": MessageLookupByLibrary.simpleMessage(
      "Modifier la transcription",
    ),
    "edit_transcript_json_fail": MessageLookupByLibrary.simpleMessage(
      "Échec de modification de la transcription horodatée. Veuillez réessayer.",
    ),
    "edit_transcript_json_success": MessageLookupByLibrary.simpleMessage(
      "La transcription horodatée a été modifiée avec succès",
    ),
    "email_invalid": MessageLookupByLibrary.simpleMessage(
      "L\'adresse e-mail n\'est pas valide.",
    ),
    "email_sent": MessageLookupByLibrary.simpleMessage(
      "Vérifiez votre boîte de réception",
    ),
    "email_sent_success": MessageLookupByLibrary.simpleMessage(
      "Nous vous avons envoyé un lien magique pour vous connecter. Cliquez sur le lien dans votre e-mail pour continuer.",
    ),
    "enable_free": MessageLookupByLibrary.simpleMessage(
      "Activer l\'essai gratuit",
    ),
    "enables_swap": MessageLookupByLibrary.simpleMessage(
      "Permet de réorganiser les images par sélection et échange",
    ),
    "english": MessageLookupByLibrary.simpleMessage("Anglais"),
    "enter_card_count": MessageLookupByLibrary.simpleMessage(
      "Entrez le nombre de cartes",
    ),
    "enter_email": MessageLookupByLibrary.simpleMessage(
      "Entrez votre adresse e-mail...",
    ),
    "enter_feedback": MessageLookupByLibrary.simpleMessage(
      "Saisissez votre retour",
    ),
    "enter_folder_name": MessageLookupByLibrary.simpleMessage(
      "Saisissez le nom du dossier",
    ),
    "enter_new_name": MessageLookupByLibrary.simpleMessage(
      "Entrer un nouveau nom",
    ),
    "enter_quiz_count": MessageLookupByLibrary.simpleMessage(
      "Entrez le nombre de quiz",
    ),
    "enter_referral_code": MessageLookupByLibrary.simpleMessage(
      "Entrez le code de parrainage",
    ),
    "enter_slide_count": MessageLookupByLibrary.simpleMessage(
      "Entrez le nombre de diapositives",
    ),
    "enter_title": MessageLookupByLibrary.simpleMessage("Saisir un titre"),
    "enter_valid_email": MessageLookupByLibrary.simpleMessage(
      "Veuillez entrer une adresse e-mail valide",
    ),
    "error": MessageLookupByLibrary.simpleMessage("Erreur"),
    "error_connection": MessageLookupByLibrary.simpleMessage(
      "Un problème de connexion est survenu.\nVeuillez réessayer",
    ),
    "error_convert_image": MessageLookupByLibrary.simpleMessage(
      "Erreur lors de la conversion de ui.Image en image.Image",
    ),
    "error_logging_in": MessageLookupByLibrary.simpleMessage(
      "Se connecter à internet",
    ),
    "esc": MessageLookupByLibrary.simpleMessage("Échap"),
    "essential": MessageLookupByLibrary.simpleMessage("Essential"),
    "essential_lifetime": MessageLookupByLibrary.simpleMessage(
      "Essentiel à vie",
    ),
    "essential_lifetime_access": MessageLookupByLibrary.simpleMessage(
      "Essential Lifetime Access",
    ),
    "evening_content": MessageLookupByLibrary.simpleMessage(
      "Réfléchir, capturer, grandir",
    ),
    "evening_content_2": MessageLookupByLibrary.simpleMessage(
      "Les insights d\'aujourd\'hui préservés",
    ),
    "evening_content_3": MessageLookupByLibrary.simpleMessage(
      "Demain naît des notes d\'aujourd\'hui",
    ),
    "evening_content_4": MessageLookupByLibrary.simpleMessage(
      "Pensées organisées, esprit tranquille",
    ),
    "evening_content_5": MessageLookupByLibrary.simpleMessage(
      "Sauvez maintenant, remerciez plus tard",
    ),
    "evening_content_6": MessageLookupByLibrary.simpleMessage(
      "Progrès préservé",
    ),
    "every_note_you_take": MessageLookupByLibrary.simpleMessage(
      "en diapositives",
    ),
    "experience": MessageLookupByLibrary.simpleMessage("expérience"),
    "export": MessageLookupByLibrary.simpleMessage("Exporter"),
    "export_as": MessageLookupByLibrary.simpleMessage("Exporter en"),
    "export_audio": MessageLookupByLibrary.simpleMessage(
      "Exporter le fichier audio",
    ),
    "export_failed": MessageLookupByLibrary.simpleMessage(
      "Échec de l\'exportation. Veuillez réessayer plus tard.",
    ),
    "export_flashcard": MessageLookupByLibrary.simpleMessage(
      "Exporter les fiches",
    ),
    "export_mind_map": MessageLookupByLibrary.simpleMessage(
      "Exporter la carte mentale",
    ),
    "export_pdf": MessageLookupByLibrary.simpleMessage("Exporter résumé"),
    "export_quiz": MessageLookupByLibrary.simpleMessage("Exporter le quiz"),
    "export_to_pdf_share_notes": MessageLookupByLibrary.simpleMessage(
      "Exporter en PDF et Partager les notes",
    ),
    "export_transcript": MessageLookupByLibrary.simpleMessage(
      "Exporter la transcription",
    ),
    "extracting_text_from_document": MessageLookupByLibrary.simpleMessage(
      "Extraction du texte du document",
    ),
    "fail": MessageLookupByLibrary.simpleMessage("Échec"),
    "fail_create_pdf": MessageLookupByLibrary.simpleMessage(
      "Échec de la création du fichier PDF",
    ),
    "fail_to_load_document": MessageLookupByLibrary.simpleMessage(
      "Échec du chargement du document !",
    ),
    "fail_to_load_video": MessageLookupByLibrary.simpleMessage(
      "Échec du chargement de la vidéo",
    ),
    "failed_get_anonymous_user": MessageLookupByLibrary.simpleMessage(
      "Échec de l\'obtention de l\'utilisateur anonyme jwt",
    ),
    "failed_to_delete_recording": MessageLookupByLibrary.simpleMessage(
      "Erreur lors de la suppression de l\'enregistrement",
    ),
    "failed_to_load_slideshow": MessageLookupByLibrary.simpleMessage(
      "Le diaporama n\'a pas pu être chargé du système. Réessayez pour une meilleure expérience.",
    ),
    "failed_to_save_file": MessageLookupByLibrary.simpleMessage(
      "Échec de l\'enregistrement du fichier",
    ),
    "feedback": MessageLookupByLibrary.simpleMessage("Avis"),
    "file_import": MessageLookupByLibrary.simpleMessage(
      "Importation de fichiers",
    ),
    "file_save_success": MessageLookupByLibrary.simpleMessage(
      "Fichier enregistré avec succès",
    ),
    "file_size_err": MessageLookupByLibrary.simpleMessage(
      "Le fichier dépasse la taille limite. Veuillez télécharger un fichier plus petit.",
    ),
    "filter": MessageLookupByLibrary.simpleMessage("Filtrer"),
    "filter_and_sort": MessageLookupByLibrary.simpleMessage("Filtrer et trier"),
    "finalizing": MessageLookupByLibrary.simpleMessage("Finalisation.."),
    "find_and_replace": MessageLookupByLibrary.simpleMessage(
      "Rechercher et remplacer",
    ),
    "flash_card_gen_success": MessageLookupByLibrary.simpleMessage(
      "Flashcard générée avec succès",
    ),
    "flash_card_iap": MessageLookupByLibrary.simpleMessage("Jeu de flashcards"),
    "flashcard": MessageLookupByLibrary.simpleMessage("Carte"),
    "flashcard_set_not_found": MessageLookupByLibrary.simpleMessage(
      "Ensemble de cartes mémoire introuvable",
    ),
    "flashcard_sets": MessageLookupByLibrary.simpleMessage(
      "Ensembles de cartes",
    ),
    "flashcards": MessageLookupByLibrary.simpleMessage("Cartes"),
    "flashcards_for": MessageLookupByLibrary.simpleMessage("Cartes pour"),
    "focus_on": MessageLookupByLibrary.simpleMessage("Priorisez l\'important"),
    "folder": MessageLookupByLibrary.simpleMessage("Dossiers"),
    "follow_steps_to_get_rewarded": MessageLookupByLibrary.simpleMessage(
      "Suivez ces étapes pour être récompensé",
    ),
    "for_unlimited_experiences": MessageLookupByLibrary.simpleMessage(
      "pour des expériences illimitées.",
    ),
    "free": MessageLookupByLibrary.simpleMessage("Gratuit"),
    "free_30_minutes": MessageLookupByLibrary.simpleMessage(
      "Gratuit : 30 minutes par fichier",
    ),
    "free_messages": MessageLookupByLibrary.simpleMessage("messages gratuits"),
    "free_recording_limit": MessageLookupByLibrary.simpleMessage(
      "Limite d\'enregistrement gratuit",
    ),
    "free_recording_limit_details": MessageLookupByLibrary.simpleMessage(
      "Il vous reste %s minutes de transcription et de résumés AI gratuits cette semaine.",
    ),
    "free_trial": MessageLookupByLibrary.simpleMessage("Essai gratuit"),
    "free_updates": MessageLookupByLibrary.simpleMessage(
      "Mises à jour et améliorations gratuites à vie",
    ),
    "free_usage": MessageLookupByLibrary.simpleMessage(
      "Utilisation gratuite - Hebdomadaire",
    ),
    "free_user_audio": MessageLookupByLibrary.simpleMessage(
      "Les utilisateurs gratuits peuvent transcrire et résumer des audios jusqu\'à 30 minutes",
    ),
    "free_user_can": MessageLookupByLibrary.simpleMessage(
      "Les utilisateurs gratuits peuvent résumer 1 vidéo YouTube (max 30 min) par jour.",
    ),
    "friendly": MessageLookupByLibrary.simpleMessage("Amical"),
    "friendly_description": MessageLookupByLibrary.simpleMessage(
      "Conversationnel avec émojis",
    ),
    "front_content": MessageLookupByLibrary.simpleMessage("Vous avez obtenu "),
    "future_features": MessageLookupByLibrary.simpleMessage(
      "Les fonctionnalités futures pourraient avoir des limites d\'utilisation",
    ),
    "gen_ai": MessageLookupByLibrary.simpleMessage("Génération IA..."),
    "gen_ai_voice": MessageLookupByLibrary.simpleMessage(
      "Génération de la voix IA",
    ),
    "gen_quiz_bgr": MessageLookupByLibrary.simpleMessage(
      "Génération de l\'arrière-plan du quiz",
    ),
    "generate_audio": MessageLookupByLibrary.simpleMessage("Générer l\'audio"),
    "generate_content": MessageLookupByLibrary.simpleMessage(
      "Créer des résumés intelligents de contenu YouTube",
    ),
    "generate_note_fail": MessageLookupByLibrary.simpleMessage(
      "Échec de la génération des notes IA !",
    ),
    "generate_shorts_step_1": MessageLookupByLibrary.simpleMessage(
      "Création de votre histoire...",
    ),
    "generate_shorts_step_2": MessageLookupByLibrary.simpleMessage(
      "Ajout de la voix parfaite...",
    ),
    "generate_shorts_step_3": MessageLookupByLibrary.simpleMessage(
      "Embellissement visuel ! Cette vidéo mérite d\'être partagée #NoteXAI",
    ),
    "generate_shorts_study_guides": MessageLookupByLibrary.simpleMessage(
      "Générer des Shorts et Guides d\'étude",
    ),
    "generate_transcript_notes": MessageLookupByLibrary.simpleMessage(
      "Nous générons une transcription, des notes et un guide d\'étude",
    ),
    "generate_video": MessageLookupByLibrary.simpleMessage("Générer la vidéo"),
    "generating_ai_note": MessageLookupByLibrary.simpleMessage(
      "Génération de la note IA",
    ),
    "generating_summary": MessageLookupByLibrary.simpleMessage(
      "Génération du résumé IA..",
    ),
    "get_fail": MessageLookupByLibrary.simpleMessage(
      "Échec de la récupération des quiz/cartes/cartes mentales. Veuillez réessayer !",
    ),
    "get_more_done": MessageLookupByLibrary.simpleMessage("Accomplir plus"),
    "get_more_done_stay_on_track": MessageLookupByLibrary.simpleMessage(
      "Accomplir plus, rester focalisé",
    ),
    "get_now": MessageLookupByLibrary.simpleMessage("OBTENIR MAINTEANT"),
    "get_offer_now": MessageLookupByLibrary.simpleMessage(
      "Obtenir l\'offre maintenant",
    ),
    "get_start": MessageLookupByLibrary.simpleMessage("Commencer"),
    "go_back": MessageLookupByLibrary.simpleMessage("Retour"),
    "go_email": MessageLookupByLibrary.simpleMessage("Aller à l\'email"),
    "go_pro": MessageLookupByLibrary.simpleMessage("Devenir PRO"),
    "go_unlimited": MessageLookupByLibrary.simpleMessage(
      "Passez à l\'illimité !",
    ),
    "good_afternoon": MessageLookupByLibrary.simpleMessage("Bon après-midi !"),
    "good_evening": MessageLookupByLibrary.simpleMessage("Bonsoir !"),
    "good_morning": MessageLookupByLibrary.simpleMessage("Bonjour !"),
    "got_it": MessageLookupByLibrary.simpleMessage("Compris !"),
    "hard": MessageLookupByLibrary.simpleMessage("Difficile"),
    "hello_welcome": MessageLookupByLibrary.simpleMessage("Bienvenue 👋"),
    "help_legal": MessageLookupByLibrary.simpleMessage("Aide & Légal"),
    "help_us_grow": MessageLookupByLibrary.simpleMessage(
      "Aidez-nous à grandir !",
    ),
    "hi": MessageLookupByLibrary.simpleMessage("Bonjour"),
    "hope_enjoy_app": MessageLookupByLibrary.simpleMessage(
      "Nous espérons que vous appréciez notre application, merci pour votre soutien !",
    ),
    "hours": MessageLookupByLibrary.simpleMessage("Heures"),
    "how_will_you_use_notex": MessageLookupByLibrary.simpleMessage(
      "Comment utiliser NoteX ?",
    ),
    "http_failed": MessageLookupByLibrary.simpleMessage(
      "Échec de la requête HTTP. Veuillez réessayer plus tard.",
    ),
    "idea1": MessageLookupByLibrary.simpleMessage(
      "Connectez-vous avec votre compte Google ou Apple si ce n\'est pas déjà fait",
    ),
    "idea2": MessageLookupByLibrary.simpleMessage(
      "Fournissez une brève description du problème",
    ),
    "idea3": MessageLookupByLibrary.simpleMessage(
      "Incluez les détails pertinents (appareil, version de l\'OS)",
    ),
    "idea4": MessageLookupByLibrary.simpleMessage(
      "Indiquez quand le problème a commencé",
    ),
    "idea5": MessageLookupByLibrary.simpleMessage(
      "Envoyez-nous un email directement à",
    ),
    "image": MessageLookupByLibrary.simpleMessage("Image"),
    "image_jpeg": MessageLookupByLibrary.simpleMessage("Image (.jpeg)"),
    "image_png": MessageLookupByLibrary.simpleMessage("Image (.png)"),
    "image_quality_too_low": MessageLookupByLibrary.simpleMessage(
      "Qualité d\'image trop basse. Utilisez une image de meilleure qualité !",
    ),
    "image_too_large": MessageLookupByLibrary.simpleMessage(
      "Image trop grande. Téléchargez une image de moins de 10 Mo.",
    ),
    "images_have_been_uploaded": m1,
    "import_note_links": MessageLookupByLibrary.simpleMessage(
      "Importer des liens de notes",
    ),
    "import_notes": MessageLookupByLibrary.simpleMessage(
      "Importer Notes Partagées",
    ),
    "improve_responses": MessageLookupByLibrary.simpleMessage(
      "Vos réponses nous aideront à améliorer",
    ),
    "initializing_camera": MessageLookupByLibrary.simpleMessage(
      "Initialisation de la caméra...",
    ),
    "insight_instantly": MessageLookupByLibrary.simpleMessage(
      "Heures de contenu en aperçu instantané",
    ),
    "insights_instantly": MessageLookupByLibrary.simpleMessage(
      "aperçus instantanés",
    ),
    "instant_answers_from_your": MessageLookupByLibrary.simpleMessage(
      "Réponses de vos",
    ),
    "instant_answers_from_your_meeting_data":
        MessageLookupByLibrary.simpleMessage("Réponses de vos réunions"),
    "instant_answers_meeting": MessageLookupByLibrary.simpleMessage(
      "Réponses de réunion",
    ),
    "instantly": MessageLookupByLibrary.simpleMessage("instantanément"),
    "interactive_ai_flashcards": MessageLookupByLibrary.simpleMessage(
      "Carte mentale interactive IA",
    ),
    "interactive_flash": MessageLookupByLibrary.simpleMessage(
      "Cartes interactives",
    ),
    "interactive_flashcards": MessageLookupByLibrary.simpleMessage(
      "Cartes interactives illimitées, cartes mentales",
    ),
    "interactive_flashcards_quiz": MessageLookupByLibrary.simpleMessage(
      "Cartes et quiz",
    ),
    "introduce_guidance": MessageLookupByLibrary.simpleMessage(
      "Merci de nous avoir contactés. Pour nous aider à enquêter et résoudre votre problème plus efficacement, veuillez suivre ces étapes :",
    ),
    "introduce_guidance2": MessageLookupByLibrary.simpleMessage(
      "Nous comprenons à quel point il peut être frustrant de rencontrer des problèmes, en particulier lorsqu\'ils concernent vos notes ou enregistrements importants. Notre équipe de support est prête à résoudre vos problèmes, généralement en moins de 12 heures.",
    ),
    "inv_audio": MessageLookupByLibrary.simpleMessage(
      "Fichier audio invalide. Veuillez télécharger un format pris en charge.",
    ),
    "inv_yt_url": MessageLookupByLibrary.simpleMessage(
      "URL YouTube invalide. Veuillez fournir un lien valide.",
    ),
    "invalid_code": MessageLookupByLibrary.simpleMessage(
      "Code invalide. Essayez à nouveau.",
    ),
    "invalid_file_type": MessageLookupByLibrary.simpleMessage(
      "Le fichier téléchargé est au mauvais format. Veuillez réessayer.",
    ),
    "invalid_token": MessageLookupByLibrary.simpleMessage("Jeton invalide"),
    "invite_friends": MessageLookupByLibrary.simpleMessage(
      "Invitez des amis - vous débloquez tous les deux des",
    ),
    "join_discord": MessageLookupByLibrary.simpleMessage("Rejoindre Discord"),
    "join_noteX_ai_lets_level_up_together":
        MessageLookupByLibrary.simpleMessage(
          "Rejoignez NoteX AI et progressons ensemble !",
        ),
    "language": MessageLookupByLibrary.simpleMessage("Langue"),
    "language_tip": MessageLookupByLibrary.simpleMessage(
      "Choisissez la langue principale dans votre audio pour de meilleurs résultats",
    ),
    "language_tip_1": MessageLookupByLibrary.simpleMessage(
      "Choisissez la langue principale pour des résultats de transcription optimaux",
    ),
    "language_tip_2": MessageLookupByLibrary.simpleMessage(
      "Pour une conversation mixte, choisissez une option multilingue",
    ),
    "language_tip_3": MessageLookupByLibrary.simpleMessage(
      "Les appels mettront automatiquement l\'enregistrement en pause. Retournez à l\'application pour reprendre",
    ),
    "latest_ai_models": MessageLookupByLibrary.simpleMessage(
      "Derniers modèles d\'IA",
    ),
    "learn_faster_through": MessageLookupByLibrary.simpleMessage(
      "Apprendre plus vite",
    ),
    "learn_faster_through_active_recall": MessageLookupByLibrary.simpleMessage(
      "Apprenez plus vite par rappel actif",
    ),
    "learn_smart": MessageLookupByLibrary.simpleMessage(
      "Apprendre intelligemment",
    ),
    "learn_unlimited": MessageLookupByLibrary.simpleMessage(
      "Apprenez intelligemment avec l\'offre illimitée !",
    ),
    "lecture_notes_study_materials": MessageLookupByLibrary.simpleMessage(
      "Notes et supports de cours",
    ),
    "let_ai_handle": MessageLookupByLibrary.simpleMessage(
      "Laissez l\'IA gérer les détails",
    ),
    "let_note_ai": MessageLookupByLibrary.simpleMessage(
      "Laissez NoteX IA transformer l\'information",
    ),
    "let_start": MessageLookupByLibrary.simpleMessage("Commençons"),
    "lets_create_your_first_ai_note": MessageLookupByLibrary.simpleMessage(
      "Créons votre première note IA !",
    ),
    "lifetime": MessageLookupByLibrary.simpleMessage("Plan à Vie"),
    "lifetime_pro_access_level_up_together":
        MessageLookupByLibrary.simpleMessage(
          "Accès Pro à vie ! Progressons ensemble ✨",
        ),
    "lifetime_setting": MessageLookupByLibrary.simpleMessage("à vie"),
    "lifetime_spots_remaining": MessageLookupByLibrary.simpleMessage(
      "places à vie disponibles",
    ),
    "light": MessageLookupByLibrary.simpleMessage("Clair"),
    "limited_notes": MessageLookupByLibrary.simpleMessage(
      "Notes limitées par jour",
    ),
    "limited_offer": MessageLookupByLibrary.simpleMessage("Offre limitée"),
    "limited_time": MessageLookupByLibrary.simpleMessage("Temps limité"),
    "link": MessageLookupByLibrary.simpleMessage("Lien"),
    "link_error": MessageLookupByLibrary.simpleMessage("Erreur de lien"),
    "link_expired": MessageLookupByLibrary.simpleMessage(
      "Le lien e-mail a expiré.",
    ),
    "link_invalid": MessageLookupByLibrary.simpleMessage(
      "Le lien utilisé est invalide, a peut-être expiré ou a déjà été utilisé. Demandez un nouveau lien et réessayez.",
    ),
    "loading": MessageLookupByLibrary.simpleMessage("Chargement"),
    "loading_content": MessageLookupByLibrary.simpleMessage(
      "Chargement du Contenu...",
    ),
    "local_recording": MessageLookupByLibrary.simpleMessage(
      "Capture intelligente",
    ),
    "login_failed": MessageLookupByLibrary.simpleMessage("Échec de connexion."),
    "login_info_1": MessageLookupByLibrary.simpleMessage(
      "Accédez à vos notes depuis n\'importe quel appareil",
    ),
    "login_info_2": MessageLookupByLibrary.simpleMessage(
      "Sécurité de niveau entreprise propulsée par AWS",
    ),
    "login_info_3": MessageLookupByLibrary.simpleMessage(
      "Vos données restent privées",
    ),
    "login_info_4": MessageLookupByLibrary.simpleMessage(
      "Accès Web sur notexapp.com",
    ),
    "login_success": MessageLookupByLibrary.simpleMessage(
      "Connexion réussie !",
    ),
    "login_title": MessageLookupByLibrary.simpleMessage(
      "Maximisez la Productivité, Partout !",
    ),
    "login_title_2": MessageLookupByLibrary.simpleMessage(
      "Découvrez NoteX 2.0",
    ),
    "login_unsuccessful": MessageLookupByLibrary.simpleMessage(
      "Échec de connexion. Réessayez ou utilisez une autre méthode",
    ),
    "logout": MessageLookupByLibrary.simpleMessage("Déconnexion"),
    "logout_detail": MessageLookupByLibrary.simpleMessage(
      "Êtes-vous sûr de vouloir vous déconnecter ?",
    ),
    "logout_question_mark": MessageLookupByLibrary.simpleMessage(
      "Se déconnecter ?",
    ),
    "loved_by": MessageLookupByLibrary.simpleMessage("Apprécié par "),
    "make_the_interface_feel_more_like_you": MessageLookupByLibrary.simpleMessage(
      "Rendez l\'interface plus personnelle — avec des préférences de thème, de police et de disposition à portée de main.",
    ),
    "making_amazing": MessageLookupByLibrary.simpleMessage(
      "On le rend incroyable ! Cette vidéo de quiz vaudra la peine d\'être partagée #NoteXAI",
    ),
    "manage_recordings": MessageLookupByLibrary.simpleMessage(
      "Gérer les enregistrements",
    ),
    "map_all_together": MessageLookupByLibrary.simpleMessage(
      "Cartographie de l\'ensemble",
    ),
    "markdown_md": MessageLookupByLibrary.simpleMessage("Markdown (.md)"),
    "max_30_cards_per_set": MessageLookupByLibrary.simpleMessage(
      "Maximum 30 cartes par ensemble",
    ),
    "max_30_quiz_sets": MessageLookupByLibrary.simpleMessage(
      "Maximum de 30 questions par ensemble",
    ),
    "max_3_flashcard_sets": MessageLookupByLibrary.simpleMessage(
      "Maximum 3 ensembles de cartes",
    ),
    "max_3_quiz_sets": MessageLookupByLibrary.simpleMessage(
      "Maximum de 3 ensembles de quiz",
    ),
    "max_60_min_per_file": MessageLookupByLibrary.simpleMessage(
      "* max 60 min par fichier",
    ),
    "max_ai": MessageLookupByLibrary.simpleMessage(
      "Transcription IA maximale :",
    ),
    "maybe_later": MessageLookupByLibrary.simpleMessage("Plus tard"),
    "medium": MessageLookupByLibrary.simpleMessage("Moyen"),
    "meeting_data": MessageLookupByLibrary.simpleMessage("données réunion"),
    "migrating_your_notes": MessageLookupByLibrary.simpleMessage(
      "Migration de vos notes...",
    ),
    "migration_complete": MessageLookupByLibrary.simpleMessage(
      "Migration terminée !",
    ),
    "mind_map": MessageLookupByLibrary.simpleMessage("Carte mentale"),
    "mind_map_gen_success": MessageLookupByLibrary.simpleMessage(
      "Carte mentale générée avec succès",
    ),
    "mind_map_iap": MessageLookupByLibrary.simpleMessage("Carte mentale"),
    "mind_map_study": MessageLookupByLibrary.simpleMessage(
      "Cartes mentales, \nGuides d\'étude",
    ),
    "minute_60_per_file": MessageLookupByLibrary.simpleMessage(
      "Essentiel : 60 minutes par fichier",
    ),
    "minute_free": MessageLookupByLibrary.simpleMessage(
      "Vous avez utilisé toutes vos 30 minutes de transcriptions et résumés AI gratuits cette semaine. Passez à Pro pour un accès illimité ou attendez la réinitialisation de votre quota gratuit.",
    ),
    "minutes": MessageLookupByLibrary.simpleMessage("Minutes"),
    "minutes_free_left": MessageLookupByLibrary.simpleMessage(
      "Minutes gratuites restantes",
    ),
    "minutes_remaining": MessageLookupByLibrary.simpleMessage(
      "minutes restantes",
    ),
    "mixed": MessageLookupByLibrary.simpleMessage("Mixte"),
    "month": MessageLookupByLibrary.simpleMessage("mois"),
    "monthly": MessageLookupByLibrary.simpleMessage("Mensuel"),
    "more_summarize": MessageLookupByLibrary.simpleMessage(
      "Résumer des réunions, podcasts, tutoriels, etc.",
    ),
    "morning_content": MessageLookupByLibrary.simpleMessage(
      "Capturez l\'éclat d\'aujourd\'hui",
    ),
    "morning_content_2": MessageLookupByLibrary.simpleMessage(
      "Esprit clair, chemin clair",
    ),
    "morning_content_3": MessageLookupByLibrary.simpleMessage(
      "Les notes d\'aujourd\'hui façonnent demain",
    ),
    "morning_content_4": MessageLookupByLibrary.simpleMessage(
      "Première pensée, meilleure pensée",
    ),
    "morning_content_5": MessageLookupByLibrary.simpleMessage(
      "Commencer avec clarté",
    ),
    "morning_content_6": MessageLookupByLibrary.simpleMessage(
      "Des idées qui valent la peine",
    ),
    "most_popular": MessageLookupByLibrary.simpleMessage("Populaire"),
    "multi_language": MessageLookupByLibrary.simpleMessage("Multilingue"),
    "multiple_people_detected": MessageLookupByLibrary.simpleMessage(
      "Plusieurs personnes détectées. Téléchargez une image d\'une seule personne !",
    ),
    "multiply_knowledge_with_friends": MessageLookupByLibrary.simpleMessage(
      "Multipliez vos connaissances",
    ),
    "my_notes": MessageLookupByLibrary.simpleMessage("Mes notes"),
    "name": MessageLookupByLibrary.simpleMessage("Nom"),
    "network_error": MessageLookupByLibrary.simpleMessage(
      "Erreur réseau. Vérifiez votre connexion internet et réessayez.",
    ),
    "neutral": MessageLookupByLibrary.simpleMessage("Neutre"),
    "neutral_description": MessageLookupByLibrary.simpleMessage(
      "Présentation directe et factuelle",
    ),
    "new_new": MessageLookupByLibrary.simpleMessage("Nouveau"),
    "new_note": MessageLookupByLibrary.simpleMessage("Nouvelle note"),
    "new_recording": MessageLookupByLibrary.simpleMessage(
      "Nouvel enregistrement - ",
    ),
    "newest_first": MessageLookupByLibrary.simpleMessage("Les plus récents"),
    "next_bill_date": m2,
    "no": MessageLookupByLibrary.simpleMessage("Non"),
    "no_generated": MessageLookupByLibrary.simpleMessage(
      "Aucun quiz/fiche créé. Appuyez pour créer maintenant !",
    ),
    "no_input": MessageLookupByLibrary.simpleMessage(
      "Aucune entrée fournie. Téléchargez un fichier audio ou saisissez une URL YouTube.",
    ),
    "no_internet": MessageLookupByLibrary.simpleMessage(
      "Pas de connexion internet",
    ),
    "no_internet_connection": MessageLookupByLibrary.simpleMessage(
      "Pas de connexion Internet !",
    ),
    "no_notes_found": MessageLookupByLibrary.simpleMessage(
      "Aucune note trouvée sous ce filtre.\nVeuillez réinitialiser la sélection du filtre",
    ),
    "no_notes_in_folder": MessageLookupByLibrary.simpleMessage(
      "Il n\'y a pas de notes dans ce dossier.",
    ),
    "no_payment_now": MessageLookupByLibrary.simpleMessage(
      "✓ Aucun paiement maintenant",
    ),
    "no_person_detected": MessageLookupByLibrary.simpleMessage(
      "Aucune personne détectée. Téléchargez une image avec une personne !",
    ),
    "no_recording_credit": MessageLookupByLibrary.simpleMessage(
      "Utilisation d\'enregistrement insuffisante. Veuillez passer à un plan supérieur.",
    ),
    "no_recordings": MessageLookupByLibrary.simpleMessage(
      "Aucune enregistrement",
    ),
    "no_results_found": MessageLookupByLibrary.simpleMessage(
      "Aucun résultat trouvé pour",
    ),
    "no_speech_detected": MessageLookupByLibrary.simpleMessage(
      "Aucune parole détectée",
    ),
    "no_summary": MessageLookupByLibrary.simpleMessage(
      "Aucun résumé disponible pour cette note.",
    ),
    "no_transcript": MessageLookupByLibrary.simpleMessage(
      "Aucune transcription disponible pour cette note.",
    ),
    "no_upload_credit": MessageLookupByLibrary.simpleMessage(
      "Utilisation de téléchargement insuffisante. Veuillez passer à un plan supérieur.",
    ),
    "no_url_provided": MessageLookupByLibrary.simpleMessage(
      "Aucune URL d\'exportation n\'a été fournie.",
    ),
    "no_voice_available": MessageLookupByLibrary.simpleMessage(
      "Aucune voix disponible",
    ),
    "not_found_audio": MessageLookupByLibrary.simpleMessage(
      "Fichier audio introuvable",
    ),
    "not_open_mail": MessageLookupByLibrary.simpleMessage(
      "Impossible d\'ouvrir l\'email !",
    ),
    "not_open_web": MessageLookupByLibrary.simpleMessage(
      "Impossible d\'ouvrir le web !",
    ),
    "not_summarized_note": MessageLookupByLibrary.simpleMessage(
      "Résumé manquant ! Cliquez sur le bouton pour activer l\'IA 👇",
    ),
    "note": MessageLookupByLibrary.simpleMessage("Note"),
    "noteX": MessageLookupByLibrary.simpleMessage("NoteX"),
    "noteX_lifetime_essential": MessageLookupByLibrary.simpleMessage(
      "NoteX Essentiel à Vie",
    ),
    "noteX_pro_lifetime": MessageLookupByLibrary.simpleMessage(
      "NoteX Pro à Vie",
    ),
    "note_404": MessageLookupByLibrary.simpleMessage(
      "Note introuvable. Veuillez vérifier l\'ID de la note et réessayer.",
    ),
    "note_not_ready": MessageLookupByLibrary.simpleMessage(
      "La note n\'est pas prête pour l\'exportation. Veuillez attendre que le traitement soit terminé.",
    ),
    "note_reminders": MessageLookupByLibrary.simpleMessage("Rappels"),
    "note_sharing": MessageLookupByLibrary.simpleMessage("Partage de notes"),
    "note_tabs": MessageLookupByLibrary.simpleMessage("Onglets de notes"),
    "note_taker": MessageLookupByLibrary.simpleMessage("#1 Prise de notes IA"),
    "notes": MessageLookupByLibrary.simpleMessage("notes"),
    "notex_empty": MessageLookupByLibrary.simpleMessage("NoteX vide"),
    "notex_experience": MessageLookupByLibrary.simpleMessage(
      "votre expérience avec NoteX",
    ),
    "nothing_restore": MessageLookupByLibrary.simpleMessage("Rien à restaurer"),
    "noti_default_description": MessageLookupByLibrary.simpleMessage(
      "Préparez-vous et commencez à enregistrer! 🚀",
    ),
    "noti_default_title": MessageLookupByLibrary.simpleMessage(
      "C\'est le moment d\'enregistrer",
    ),
    "noti_req_description": MessageLookupByLibrary.simpleMessage(
      "Les notifications peuvent inclure des alertes, des sons et des badges d\'icônes. Ces paramètres peuvent être configurés dans les paramètres.",
    ),
    "noti_req_title": MessageLookupByLibrary.simpleMessage(
      "‘NoteX’ Souhaite Vous Envoyer des Notifications",
    ),
    "notifications": MessageLookupByLibrary.simpleMessage("Notifications"),
    "notifications_note_created": MessageLookupByLibrary.simpleMessage(
      "Vos notes ont été créées avec succès",
    ),
    "notifications_note_ready": MessageLookupByLibrary.simpleMessage(
      "Notifier lorsque les notes sont prêtes",
    ),
    "nova_ai_assistant_mind_mapping": MessageLookupByLibrary.simpleMessage(
      "Assistant Nova IA & Mapping Mental",
    ),
    "nova_ai_chat": MessageLookupByLibrary.simpleMessage("Chat Nova IA"),
    "nova_chat": MessageLookupByLibrary.simpleMessage("Chat Nova"),
    "of_index": MessageLookupByLibrary.simpleMessage("de"),
    "of_user": MessageLookupByLibrary.simpleMessage(" des utilisateurs"),
    "offer_expires": MessageLookupByLibrary.simpleMessage("Offre expire dans"),
    "ok": MessageLookupByLibrary.simpleMessage("OK"),
    "oldest_first": MessageLookupByLibrary.simpleMessage("Les plus anciens"),
    "on_track": MessageLookupByLibrary.simpleMessage("rester focalisé"),
    "on_your_android": MessageLookupByLibrary.simpleMessage(
      "Sur votre téléphone ou tablette Android, ouvrez le Google Play Store",
    ),
    "onboarding_generate_audio_video_content":
        MessageLookupByLibrary.simpleMessage("Transformer les notes en"),
    "onboarding_generate_audio_video_full_content":
        MessageLookupByLibrary.simpleMessage(
          "Transformer les notes en contenu engageant",
        ),
    "onboarding_generate_audio_video_sub_content":
        MessageLookupByLibrary.simpleMessage("contenu engageant"),
    "onboarding_generate_audio_video_title":
        MessageLookupByLibrary.simpleMessage("Générer audio et vidéo"),
    "once_in_a_lifetime_offer": MessageLookupByLibrary.simpleMessage(
      "Offre unique",
    ),
    "one_per_day": MessageLookupByLibrary.simpleMessage("1 par jour"),
    "one_time_payment": MessageLookupByLibrary.simpleMessage("Paiement unique"),
    "only": MessageLookupByLibrary.simpleMessage("Plus que"),
    "only_today": MessageLookupByLibrary.simpleMessage(
      "Seulement aujourd\'hui",
    ),
    "oops_something_went_wrong": MessageLookupByLibrary.simpleMessage(
      "Oups !\nQuelque chose s\'est mal passé",
    ),
    "open_now": MessageLookupByLibrary.simpleMessage("Ouvrir maintenant"),
    "open_youtube": MessageLookupByLibrary.simpleMessage("Ouvrir YouTube"),
    "opportunities": MessageLookupByLibrary.simpleMessage("opportunités"),
    "or": MessageLookupByLibrary.simpleMessage("ou"),
    "or_upper": MessageLookupByLibrary.simpleMessage("Ou"),
    "organize_assign_action_items": MessageLookupByLibrary.simpleMessage(
      "Organiser les tâches",
    ),
    "organize_assign_items": MessageLookupByLibrary.simpleMessage(
      "Organiser tâches",
    ),
    "others": MessageLookupByLibrary.simpleMessage("Autres"),
    "output_language": MessageLookupByLibrary.simpleMessage("Langue de Sortie"),
    "pace": MessageLookupByLibrary.simpleMessage("rythme"),
    "paste": MessageLookupByLibrary.simpleMessage("Coller"),
    "paste_url_here": MessageLookupByLibrary.simpleMessage("Coller l\'URL ici"),
    "paste_youtube_link": MessageLookupByLibrary.simpleMessage(
      "Coller un lien Youtube",
    ),
    "payment_required": MessageLookupByLibrary.simpleMessage(
      "Vous avez épuisé vos crédits gratuits",
    ),
    "payment_successfully": MessageLookupByLibrary.simpleMessage(
      "Paiement réussi !",
    ),
    "pdf_export": MessageLookupByLibrary.simpleMessage("Exportation PDF"),
    "pdf_pdf": MessageLookupByLibrary.simpleMessage("PDF (.pdf)"),
    "per_year": MessageLookupByLibrary.simpleMessage(" par an"),
    "period": MessageLookupByLibrary.simpleMessage(" de %s min"),
    "personalized_learning": MessageLookupByLibrary.simpleMessage(
      "Pratiquez librement",
    ),
    "personalized_learning_at": MessageLookupByLibrary.simpleMessage(
      "Pratiquez à votre rythme pour un A+",
    ),
    "photos": MessageLookupByLibrary.simpleMessage("Photos"),
    "pick_specific_language": MessageLookupByLibrary.simpleMessage(
      "Choisissez une langue spécifique dans l\'audio pour une meilleure précision de transcription",
    ),
    "plan": MessageLookupByLibrary.simpleMessage("Forfait"),
    "please_select_a_language": MessageLookupByLibrary.simpleMessage(
      "Veuillez d\'abord sélectionner une langue pour que nous puissions transcrire l\'audio avec précision !",
    ),
    "please_select_a_youtube_language": MessageLookupByLibrary.simpleMessage(
      "Veuillez sélectionner une langue de résumé. C\'est la langue que vous verrez dans le résultat du résumé",
    ),
    "please_try_again": MessageLookupByLibrary.simpleMessage(
      "Veuillez réessayer",
    ),
    "please_wait": MessageLookupByLibrary.simpleMessage("Veuillez patienter"),
    "podcast": MessageLookupByLibrary.simpleMessage("Podcast"),
    "podcast_name": MessageLookupByLibrary.simpleMessage("Nom du podcast"),
    "policy": MessageLookupByLibrary.simpleMessage("Politique"),
    "premium_features": MessageLookupByLibrary.simpleMessage(
      "Essayez les fonctionnalités premium pour voir la différence",
    ),
    "preparing_video": MessageLookupByLibrary.simpleMessage(
      "Préparation de la vidéo...",
    ),
    "press_back_again_to_exit": MessageLookupByLibrary.simpleMessage(
      "Appuyez de nouveau pour quitter !",
    ),
    "preview_only": MessageLookupByLibrary.simpleMessage(
      "Aperçu uniquement. L\'arrière-plan sera généré par l\'IA en fonction du contenu",
    ),
    "priority_processing": MessageLookupByLibrary.simpleMessage(
      "Traitement prioritaire",
    ),
    "privacy_policy": MessageLookupByLibrary.simpleMessage(
      "Politique de confidentialité",
    ),
    "private": MessageLookupByLibrary.simpleMessage("Privé"),
    "pro": MessageLookupByLibrary.simpleMessage("Plan PRO"),
    "proLite": MessageLookupByLibrary.simpleMessage("Pro Lite"),
    "pro_01": MessageLookupByLibrary.simpleMessage("Pro"),
    "pro_6_hours": MessageLookupByLibrary.simpleMessage(
      "Pro : 6 heures par fichier",
    ),
    "pro_access_life_time": MessageLookupByLibrary.simpleMessage(
      "ACCÈS PRO À VIE",
    ),
    "pro_lifetime": MessageLookupByLibrary.simpleMessage("PRO à vie"),
    "process_your_document": MessageLookupByLibrary.simpleMessage(
      "Traitement de votre document...",
    ),
    "process_your_text": MessageLookupByLibrary.simpleMessage(
      "Traitement de votre texte...",
    ),
    "processing_content": MessageLookupByLibrary.simpleMessage(
      "Traitement du contenu...",
    ),
    "processing_file": MessageLookupByLibrary.simpleMessage(
      "Traitement du fichier..",
    ),
    "processing_image": MessageLookupByLibrary.simpleMessage(
      "Traitement de l\'image...",
    ),
    "processing_note_audio_file": MessageLookupByLibrary.simpleMessage(
      "Traitement de votre fichier audio...",
    ),
    "processing_note_recording": MessageLookupByLibrary.simpleMessage(
      "Traitement de votre enregistrement...",
    ),
    "processing_note_youtube": MessageLookupByLibrary.simpleMessage(
      "Traitement de la vidéo YouTube...",
    ),
    "processing_web_link": MessageLookupByLibrary.simpleMessage(
      "Traitement du lien",
    ),
    "producing_flashcards": MessageLookupByLibrary.simpleMessage(
      "Création des cartes IA..",
    ),
    "professional": MessageLookupByLibrary.simpleMessage("Professionnel"),
    "professional_description": MessageLookupByLibrary.simpleMessage(
      "Langage formel adapté au contexte professionnel",
    ),
    "professional_style": MessageLookupByLibrary.simpleMessage("Professionnel"),
    "public": MessageLookupByLibrary.simpleMessage("Public"),
    "purchase_fail": MessageLookupByLibrary.simpleMessage(
      "Échec de l\'achat ! Veuillez réessayer !",
    ),
    "purchase_init_fail": MessageLookupByLibrary.simpleMessage(
      "Oups ! Impossible de démarrer l\'achat. Veuillez réessayer",
    ),
    "purpose_using": MessageLookupByLibrary.simpleMessage(
      "objectif en utilisant ",
    ),
    "quarter": MessageLookupByLibrary.simpleMessage("trimestre"),
    "quarterly": MessageLookupByLibrary.simpleMessage("Trimestriel"),
    "question": MessageLookupByLibrary.simpleMessage("Question"),
    "quick_access": MessageLookupByLibrary.simpleMessage("Accès rapide"),
    "quick_import": MessageLookupByLibrary.simpleMessage(
      "Ou importez rapidement depuis",
    ),
    "quickly": MessageLookupByLibrary.simpleMessage("concepts"),
    "quiz": MessageLookupByLibrary.simpleMessage("Quiz"),
    "quiz_count": MessageLookupByLibrary.simpleMessage("Nombre de quiz"),
    "quiz_diff": MessageLookupByLibrary.simpleMessage("Difficulté du quiz"),
    "quiz_gen_success": MessageLookupByLibrary.simpleMessage(
      "Quiz généré avec succès",
    ),
    "quiz_iap": MessageLookupByLibrary.simpleMessage("Jeu de quiz"),
    "quiz_master": MessageLookupByLibrary.simpleMessage("Maître des quiz IA"),
    "quiz_score": MessageLookupByLibrary.simpleMessage("Score du quiz"),
    "quiz_set": MessageLookupByLibrary.simpleMessage("Ensembles de quiz"),
    "quiz_set_not_found": MessageLookupByLibrary.simpleMessage(
      "Ensemble de quiz introuvable",
    ),
    "quizz_for": MessageLookupByLibrary.simpleMessage("Quiz pour"),
    "quizzes": MessageLookupByLibrary.simpleMessage("Quiz"),
    "rate": MessageLookupByLibrary.simpleMessage("Évaluer"),
    "rate_five_stars": MessageLookupByLibrary.simpleMessage(
      "Évaluez-nous 5 étoiles",
    ),
    "rate_us_on_store": MessageLookupByLibrary.simpleMessage("Évaluez-nous"),
    "rating_cmt1": MessageLookupByLibrary.simpleMessage(
      "Cette app est incroyable et s\'améliore. Merci aux développeurs pour leur travail. Je la recommande plus que toute autre app",
    ),
    "rating_cmt2": MessageLookupByLibrary.simpleMessage(
      "J\'adore absolument cette application ! Le compagnon parfait pour toutes mes réunions.",
    ),
    "rating_cmt3": MessageLookupByLibrary.simpleMessage(
      "A réduit mon temps d\'étude de moitié. Plus de temps pour les pauses café !",
    ),
    "rating_cmt4": MessageLookupByLibrary.simpleMessage(
      "Cette application est absolument époustouflante ! Non seulement elle réussit la transcription, mais elle élève les choses à un autre niveau avec des résumés, des plans et des éléments d\'action incroyables. Pure génie !",
    ),
    "rating_cmt5": MessageLookupByLibrary.simpleMessage(
      "Merveilleux et puissant! Tout ce qu\'on veut et plus encore",
    ),
    "rating_cmt6": MessageLookupByLibrary.simpleMessage(
      "Testé avec une présentation YouTube. En secondes, transcription, carte mentale et flashcards. Je l\'utiliserai quotidiennement",
    ),
    "rating_cmt7": MessageLookupByLibrary.simpleMessage(
      "Meilleure app de notes jusqu\'ici. Pleine de fonctionnalités utiles",
    ),
    "rating_cmt8": MessageLookupByLibrary.simpleMessage(
      "Capture chaque détail des cours de biologie. La fonction résumé est parfaite pour les examens",
    ),
    "rating_sub_context_1": MessageLookupByLibrary.simpleMessage(
      "Époustouflant",
    ),
    "rating_sub_context_2": MessageLookupByLibrary.simpleMessage(
      "Meeting Professionnel",
    ),
    "rating_sub_context_3": MessageLookupByLibrary.simpleMessage(
      "Économiseur de Temps",
    ),
    "rating_sub_context_4": MessageLookupByLibrary.simpleMessage(
      "Meilleures Notes AI",
    ),
    "rating_sub_context_5": MessageLookupByLibrary.simpleMessage(
      "J\'adore cette app",
    ),
    "rating_sub_context_6": MessageLookupByLibrary.simpleMessage(
      "Meilleure app IA",
    ),
    "rating_sub_context_7": MessageLookupByLibrary.simpleMessage(
      "Meilleure prise de notes",
    ),
    "rating_sub_context_8": MessageLookupByLibrary.simpleMessage(
      "La meilleure à ce jour",
    ),
    "record": MessageLookupByLibrary.simpleMessage("Enregistrement"),
    "record_audio": MessageLookupByLibrary.simpleMessage(
      "Enregistrer un audio",
    ),
    "record_audio_coming_soon": MessageLookupByLibrary.simpleMessage(
      "Enregistrement audio (bientôt)",
    ),
    "record_over_x_min": MessageLookupByLibrary.simpleMessage(
      "Enregistrement au-delà de %s minutes",
    ),
    "record_over_x_min_details": MessageLookupByLibrary.simpleMessage(
      "Les enregistrements seront sauvegardés localement sans transcription ni résumé AI. Supprimez les limites pour traiter cet enregistrement après sa fin.",
    ),
    "record_summarize_lecture": MessageLookupByLibrary.simpleMessage(
      "Enregistrer et résumer des cours universitaires",
    ),
    "recording": MessageLookupByLibrary.simpleMessage("Enregistrement"),
    "recording_in_progress": MessageLookupByLibrary.simpleMessage(
      "Enregistrement en cours",
    ),
    "recording_in_progress_content": MessageLookupByLibrary.simpleMessage(
      "Enregistrement...",
    ),
    "recording_paused": MessageLookupByLibrary.simpleMessage(
      "Enregistrement en pause",
    ),
    "recording_paused_content": MessageLookupByLibrary.simpleMessage(
      "Appuyez pour reprendre",
    ),
    "recording_permission_denied": MessageLookupByLibrary.simpleMessage(
      "Autorisation d\'enregistrement refusée !",
    ),
    "recording_permission_denied_details": MessageLookupByLibrary.simpleMessage(
      "Accédez aux Paramètres pour autoriser",
    ),
    "recording_quality": MessageLookupByLibrary.simpleMessage(
      "Qualité de l\'enregistrement",
    ),
    "recording_schedule": MessageLookupByLibrary.simpleMessage(
      "Programme d\'enregistrement",
    ),
    "recording_voice_note": MessageLookupByLibrary.simpleMessage(
      "Enregistrement de note vocale",
    ),
    "redeem_7_days_for_0": MessageLookupByLibrary.simpleMessage(
      "Récupérer 7 jours pour 0",
    ),
    "redeem_credits": MessageLookupByLibrary.simpleMessage("Échanger"),
    "refer_now": MessageLookupByLibrary.simpleMessage("Parrainer maintenant"),
    "refer_rewards": MessageLookupByLibrary.simpleMessage(
      "Parrainez & Récompenses",
    ),
    "referral": MessageLookupByLibrary.simpleMessage("Parrainage"),
    "referral_already_used": MessageLookupByLibrary.simpleMessage(
      "Le code de parrainage a déjà été utilisé.",
    ),
    "referral_code": MessageLookupByLibrary.simpleMessage("Code de parrainage"),
    "referral_credits": MessageLookupByLibrary.simpleMessage(
      "Crédits de parrainage",
    ),
    "referral_not_found": MessageLookupByLibrary.simpleMessage(
      "Code de parrainage non trouvé.",
    ),
    "referral_self_use": MessageLookupByLibrary.simpleMessage(
      "Vous ne pouvez pas utiliser votre propre code de parrainage.",
    ),
    "referral_time_expired": MessageLookupByLibrary.simpleMessage(
      "Le code de parrainage a expiré après 24 heures.",
    ),
    "referral_validation_err": MessageLookupByLibrary.simpleMessage(
      "Erreur de validation du parrainage.",
    ),
    "reload_tap": MessageLookupByLibrary.simpleMessage(
      "Erreur, appuyez pour recharger",
    ),
    "remain_recording_length": MessageLookupByLibrary.simpleMessage(
      "30 s - 5 min restants selon la durée de l\'enregistrement...",
    ),
    "reminders_record_audio": MessageLookupByLibrary.simpleMessage(
      "Configurez des heures d\'enregistrement audio hebdomadaires",
    ),
    "remove_all_limits": MessageLookupByLibrary.simpleMessage(
      "Supprimer toutes les limites",
    ),
    "replace": MessageLookupByLibrary.simpleMessage("Remplacer"),
    "replace_all": MessageLookupByLibrary.simpleMessage("Tout remplacer"),
    "report_issue": MessageLookupByLibrary.simpleMessage(
      "Comment signaler un problème ?",
    ),
    "report_issue2": MessageLookupByLibrary.simpleMessage(
      "Nous sommes là pour vous aider :",
    ),
    "required": MessageLookupByLibrary.simpleMessage("Ex : Nom_dossier A"),
    "reset": MessageLookupByLibrary.simpleMessage("Réinit"),
    "restart_now": MessageLookupByLibrary.simpleMessage(
      "Redémarrer maintenant",
    ),
    "restore": MessageLookupByLibrary.simpleMessage("Restaurer"),
    "restore_fail_message": MessageLookupByLibrary.simpleMessage(
      "Pour obtenir de l\'aide, contactez <EMAIL>",
    ),
    "restore_fail_title": MessageLookupByLibrary.simpleMessage(
      "Aucun élément disponible pour la restauration",
    ),
    "restore_purchase": MessageLookupByLibrary.simpleMessage(
      "Restaurer l\'achat",
    ),
    "restore_success_title": MessageLookupByLibrary.simpleMessage(
      "Restauration réussie",
    ),
    "retention": MessageLookupByLibrary.simpleMessage("et la rétention"),
    "retention_quickly": MessageLookupByLibrary.simpleMessage(
      "entre les concepts",
    ),
    "retry": MessageLookupByLibrary.simpleMessage("Réessayer"),
    "sale_off": MessageLookupByLibrary.simpleMessage("RÉDUCTION"),
    "satisfied": MessageLookupByLibrary.simpleMessage(
      "Merci pour votre retour !",
    ),
    "satisfied_quality": MessageLookupByLibrary.simpleMessage(
      "Cette note est-elle claire et utile ?",
    ),
    "save": MessageLookupByLibrary.simpleMessage("Enregistrer"),
    "save_50": MessageLookupByLibrary.simpleMessage("Éco 50%"),
    "save_changes": MessageLookupByLibrary.simpleMessage(
      "Enregistrer les modifications ?",
    ),
    "save_chat": MessageLookupByLibrary.simpleMessage("Enregistrer le chat"),
    "save_file": MessageLookupByLibrary.simpleMessage("Fichier enregistré"),
    "saved_chat": MessageLookupByLibrary.simpleMessage("Chat enregistré"),
    "saved_successfully": MessageLookupByLibrary.simpleMessage(
      "Enregistré avec succès",
    ),
    "saving_recording": MessageLookupByLibrary.simpleMessage(
      "Enregistrement sauvegardé sur l\'appareil",
    ),
    "search": MessageLookupByLibrary.simpleMessage("Rechercher"),
    "search_emoji": MessageLookupByLibrary.simpleMessage("Rechercher un emoji"),
    "search_in_files": MessageLookupByLibrary.simpleMessage(
      "Rechercher dans les fichiers",
    ),
    "searching_all_notes": MessageLookupByLibrary.simpleMessage(
      "Recherche dans toutes les notes",
    ),
    "seconds": MessageLookupByLibrary.simpleMessage("Secondes"),
    "select_a_language": MessageLookupByLibrary.simpleMessage(
      "Sélectionnez une langue à utiliser pendant le processus d\'enregistrement avant de sauvegarder.",
    ),
    "select_all": MessageLookupByLibrary.simpleMessage("Tout sélectionner"),
    "select_and_reorder": MessageLookupByLibrary.simpleMessage(
      "Sélectionnez et réorganisez vos modules de notes. Un minimum de 4 onglets est requis pour continuer.",
    ),
    "select_language": MessageLookupByLibrary.simpleMessage(
      "Sélectionnez la langue",
    ),
    "select_your_note": MessageLookupByLibrary.simpleMessage(
      "Sélectionnez vos modules de notes.",
    ),
    "select_your_primary_use_case": MessageLookupByLibrary.simpleMessage(
      "Choisir votre usage",
    ),
    "server_err": MessageLookupByLibrary.simpleMessage(
      "Une erreur serveur inconnue s\'est produite.",
    ),
    "server_error": MessageLookupByLibrary.simpleMessage(
      "Une erreur s\'est produite",
    ),
    "setting": MessageLookupByLibrary.simpleMessage("Paramètres"),
    "settings": MessageLookupByLibrary.simpleMessage("Paramètres"),
    "seven_day_free": MessageLookupByLibrary.simpleMessage(
      "7 jours gratuits, puis",
    ),
    "share": MessageLookupByLibrary.simpleMessage("Partager"),
    "share_audio_file": MessageLookupByLibrary.simpleMessage(
      "Partager l\'audio",
    ),
    "share_code_friends": MessageLookupByLibrary.simpleMessage(
      "Partagez le code avec vos amis par email, réseaux sociaux ou messages.",
    ),
    "share_file": MessageLookupByLibrary.simpleMessage(
      "Partager le fichier audio",
    ),
    "share_note": MessageLookupByLibrary.simpleMessage("Partager des notes"),
    "share_note_link": MessageLookupByLibrary.simpleMessage("Partager la note"),
    "share_only": MessageLookupByLibrary.simpleMessage("Partager"),
    "share_referral_code_start_earning_credits":
        MessageLookupByLibrary.simpleMessage(
          "Partagez votre code de parrainage pour commencer à gagner des crédits !",
        ),
    "share_summary": MessageLookupByLibrary.simpleMessage("Copier le résumé"),
    "share_sync": MessageLookupByLibrary.simpleMessage(
      "Partager & Synchroniser",
    ),
    "share_transcript": MessageLookupByLibrary.simpleMessage(
      "Copier la transcription",
    ),
    "share_with_link": MessageLookupByLibrary.simpleMessage(
      "Partager avec lien:",
    ),
    "shared": MessageLookupByLibrary.simpleMessage("Partagé"),
    "sharing_export": MessageLookupByLibrary.simpleMessage(
      "Partage & Exportation",
    ),
    "short": MessageLookupByLibrary.simpleMessage("Court"),
    "short_description": MessageLookupByLibrary.simpleMessage(
      "Points clés uniquement",
    ),
    "shorts": MessageLookupByLibrary.simpleMessage("Shorts"),
    "show_your_love": MessageLookupByLibrary.simpleMessage(
      "Montrez votre soutien en laissant un",
    ),
    "signing_in": MessageLookupByLibrary.simpleMessage("Connexion en cours..."),
    "skip": MessageLookupByLibrary.simpleMessage("Passer"),
    "slide_count": MessageLookupByLibrary.simpleMessage("Créer un diaporama"),
    "slide_count_tooltip": MessageLookupByLibrary.simpleMessage(
      "Maximum de 12 diapositives par modèle",
    ),
    "slide_range": MessageLookupByLibrary.simpleMessage("Créer un diaporama"),
    "slide_show": MessageLookupByLibrary.simpleMessage("Diaporama"),
    "smart_learning": MessageLookupByLibrary.simpleMessage(
      "Apprentissage intelligent",
    ),
    "smart_note_big_ideas": MessageLookupByLibrary.simpleMessage(
      "Notes Malines, Idées Brillantes",
    ),
    "smart_quizzes": MessageLookupByLibrary.simpleMessage(
      "Quizz adaptatifs illimités",
    ),
    "smart_start": MessageLookupByLibrary.simpleMessage(
      "Pack de démarrage intelligent",
    ),
    "sort_by": MessageLookupByLibrary.simpleMessage("Trier par"),
    "special_gift": MessageLookupByLibrary.simpleMessage("Cadeau spécial"),
    "special_gift_title": MessageLookupByLibrary.simpleMessage(
      "CADEAU SPÉCIAL",
    ),
    "special_offer": MessageLookupByLibrary.simpleMessage("OFFRE\nSPÉCIALE"),
    "speech_language": MessageLookupByLibrary.simpleMessage(
      "Langue de l\'audio",
    ),
    "start_for_free": MessageLookupByLibrary.simpleMessage(
      "Commencez gratuitement",
    ),
    "start_free_trial": MessageLookupByLibrary.simpleMessage(
      "Commencer l\'essai gratuit",
    ),
    "start_my_7_day_trial": MessageLookupByLibrary.simpleMessage(
      "Commencer mon essai de 7 jours",
    ),
    "start_record": MessageLookupByLibrary.simpleMessage(
      "Commencer l\'enregistrement",
    ),
    "start_speaking": MessageLookupByLibrary.simpleMessage(
      "Commencer à parler",
    ),
    "step1": MessageLookupByLibrary.simpleMessage(
      "Dans l\'application NoteX, allez dans Paramètres.",
    ),
    "step2": MessageLookupByLibrary.simpleMessage(
      "Localisez la version de l\'application en bas (par exemple, v1.4.0(6)).",
    ),
    "step3": MessageLookupByLibrary.simpleMessage(
      "Appuyez rapidement 5 fois sur la version de l\'application.",
    ),
    "step4": MessageLookupByLibrary.simpleMessage(
      "Votre identifiant utilisateur unique sera automatiquement copié dans votre presse-papiers.",
    ),
    "step5": MessageLookupByLibrary.simpleMessage(
      "Dans votre message ci-dessous, veuillez inclure :",
    ),
    "step51": MessageLookupByLibrary.simpleMessage(
      "Votre identifiant utilisateur (collez-le depuis votre presse-papiers).",
    ),
    "step52": MessageLookupByLibrary.simpleMessage(
      "Une brève description du problème que vous rencontrez.",
    ),
    "step53": MessageLookupByLibrary.simpleMessage(
      "Tous les détails pertinents (par exemple, modèle d\'appareil, version iOS).",
    ),
    "step6": MessageLookupByLibrary.simpleMessage("Envoyez-nous un email à "),
    "student": MessageLookupByLibrary.simpleMessage("Étudiant"),
    "style": MessageLookupByLibrary.simpleMessage("Style"),
    "sub_rip": MessageLookupByLibrary.simpleMessage("SubRip (.srt)"),
    "sub_user_have_unlimited": MessageLookupByLibrary.simpleMessage(
      "Les abonnés ont un accès illimité à toutes les fonctionnalités premium sans publicité. Les non-abonnés peuvent utiliser l\'application avec des publicités et un accès limité aux fonctionnalités premium. Le paiement sera débité de votre compte Google Play à la confirmation de l\'achat. L\'abonnement sera automatiquement renouvelé sauf annulation dans les 24 heures avant la fin de la période. Toute période d\'essai non utilisée sera perdue en cas d\'achat d\'un abonnement. Vous pouvez gérer ou annuler le renouvellement automatique dans les paramètres Google Play.",
    ),
    "sub_will_auto_renew": MessageLookupByLibrary.simpleMessage(
      "L\'abonnement se renouvellera automatiquement. Annulez à tout moment.",
    ),
    "submit": MessageLookupByLibrary.simpleMessage("Envoyer"),
    "submit_button": MessageLookupByLibrary.simpleMessage("Soumettre"),
    "subscribe": MessageLookupByLibrary.simpleMessage("S\'abonner"),
    "subscribe_via_web": MessageLookupByLibrary.simpleMessage(
      "Si abonné via web, gérez sur notexapp.com/setting",
    ),
    "success": MessageLookupByLibrary.simpleMessage("Succès"),
    "successfully": MessageLookupByLibrary.simpleMessage("Succès"),
    "suggest_features": MessageLookupByLibrary.simpleMessage(
      "Suggérer des fonctionnalités",
    ),
    "suggested": MessageLookupByLibrary.simpleMessage("Suggéré"),
    "summarize_video": MessageLookupByLibrary.simpleMessage(
      "Résumer de longues vidéos YouTube",
    ),
    "summary": MessageLookupByLibrary.simpleMessage("Résumé"),
    "summary_style": MessageLookupByLibrary.simpleMessage("Style de Résumé"),
    "summary_successful": MessageLookupByLibrary.simpleMessage(
      "Résumé créé avec succès !",
    ),
    "summary_usefulness": MessageLookupByLibrary.simpleMessage(
      "Utilité du résumé",
    ),
    "supercharge": MessageLookupByLibrary.simpleMessage(
      "Atteignez plus, stressez moins",
    ),
    "support_audio": MessageLookupByLibrary.simpleMessage(
      "Types de fichiers pris en charge: .mp3, .wav, .ogg, .m4a",
    ),
    "support_for_up_to_10_images": MessageLookupByLibrary.simpleMessage(
      "Prise en charge jusqu\'à 10 images",
    ),
    "support_image": MessageLookupByLibrary.simpleMessage(
      "Types d\'images pris en charge : .png, .jpg, .heif, .heic",
    ),
    "support_over_onehundred_languages": MessageLookupByLibrary.simpleMessage(
      "Supporte 100+ langues",
    ),
    "support_youtube_and_more": MessageLookupByLibrary.simpleMessage(
      "Prend en charge YouTube, Web, TikTok, Instagram, Facebook et plus",
    ),
    "switch_mode": MessageLookupByLibrary.simpleMessage("Changer de mode"),
    "sync_from_watch": MessageLookupByLibrary.simpleMessage(
      "Synchroniser depuis l\'horloge",
    ),
    "sync_notes": MessageLookupByLibrary.simpleMessage(
      "Synchronisez les notes sur un navigateur d\'ordinateur",
    ),
    "system": MessageLookupByLibrary.simpleMessage("Système"),
    "tap_cancel": MessageLookupByLibrary.simpleMessage(
      "Touchez Annuler l\'abonnement",
    ),
    "tap_menu": MessageLookupByLibrary.simpleMessage(
      "Touchez Menu > Abonnements et sélectionnez l\'abonnement à annuler",
    ),
    "tap_the": MessageLookupByLibrary.simpleMessage("Appuyez sur"),
    "tap_the_record": MessageLookupByLibrary.simpleMessage(
      "Appuyez sur Enregistrer",
    ),
    "tap_to_record": MessageLookupByLibrary.simpleMessage(
      "Appuyez pour enregistrer vos pensées",
    ),
    "task_create_err": MessageLookupByLibrary.simpleMessage(
      "Erreur lors de la création de la tâche. Veuillez réessayer plus tard.",
    ),
    "templates": MessageLookupByLibrary.simpleMessage("Modèles"),
    "term_and_cond": MessageLookupByLibrary.simpleMessage(
      "Conditions générales",
    ),
    "terms": MessageLookupByLibrary.simpleMessage("Conditions"),
    "terms_of_sub": MessageLookupByLibrary.simpleMessage(
      "Conditions d\'abonnement",
    ),
    "terms_of_use": MessageLookupByLibrary.simpleMessage(
      "Conditions d\'utilisation",
    ),
    "text": MessageLookupByLibrary.simpleMessage("Ajouter du Texte"),
    "text_must_not_exceed_50_chars": MessageLookupByLibrary.simpleMessage(
      "Le texte ne doit pas dépasser 50 caractères",
    ),
    "thank_feedback": MessageLookupByLibrary.simpleMessage(
      "Merci pour votre retour !",
    ),
    "thinking": MessageLookupByLibrary.simpleMessage("Pensant..."),
    "thirty_min_per": MessageLookupByLibrary.simpleMessage(
      "30 min par semaine",
    ),
    "this_folder_empty": MessageLookupByLibrary.simpleMessage(
      "C\'est le moment de créer votre première note IA ! ✨",
    ),
    "this_free_trial": MessageLookupByLibrary.simpleMessage(
      "Cet essai gratuit est une offre de découverte pour les nouveaux utilisateurs. Profitez de toutes les fonctionnalités Pro pendant une semaine avant de décider.",
    ),
    "this_is_the_language": MessageLookupByLibrary.simpleMessage(
      "C\'est la langue que vous verrez dans le résultat du résumé",
    ),
    "thousands_trusted": MessageLookupByLibrary.simpleMessage(
      "Noté 4.8/5 : Approuvé par des milliers",
    ),
    "time": MessageLookupByLibrary.simpleMessage("Heure"),
    "time_black_friday": MessageLookupByLibrary.simpleMessage("22 - 30 Nov"),
    "time_black_friday_2": MessageLookupByLibrary.simpleMessage(
      "22 - 30 November",
    ),
    "time_out": MessageLookupByLibrary.simpleMessage(
      "Délai d\'attente dépassé. Veuillez réessayer.",
    ),
    "title": MessageLookupByLibrary.simpleMessage("Titre"),
    "title_error_note": MessageLookupByLibrary.simpleMessage(
      "Échec de la création de la note",
    ),
    "title_success_note": MessageLookupByLibrary.simpleMessage(
      "Notes IA créées avec succès",
    ),
    "to": MessageLookupByLibrary.simpleMessage("pour"),
    "to_day": MessageLookupByLibrary.simpleMessage("Aujourd\'hui"),
    "token_expired": MessageLookupByLibrary.simpleMessage(
      "Le jeton a expiré !",
    ),
    "tolower_credits": MessageLookupByLibrary.simpleMessage("crédits"),
    "tool_tip_language": MessageLookupByLibrary.simpleMessage(
      "Sélectionnez la langue principale avant d\'enregistrer cet enregistrement",
    ),
    "topic_option": MessageLookupByLibrary.simpleMessage("Thème (facultatif)"),
    "total": MessageLookupByLibrary.simpleMessage("Total"),
    "transcribing": MessageLookupByLibrary.simpleMessage(
      "Transcription avec la meilleure IA",
    ),
    "transcribing_audio": MessageLookupByLibrary.simpleMessage(
      "Transcription de l\'audio..",
    ),
    "transcript": MessageLookupByLibrary.simpleMessage("Transcription"),
    "transcript_language": MessageLookupByLibrary.simpleMessage(
      "Langue de transcription",
    ),
    "transcript_line_cannot_be_empty": MessageLookupByLibrary.simpleMessage(
      "La ligne de transcription ne peut pas être vide",
    ),
    "transcript_line_tool_tip": MessageLookupByLibrary.simpleMessage(
      "Cliquez sur l\'élément de transcription pour modifier",
    ),
    "transcription_precision": MessageLookupByLibrary.simpleMessage(
      "Précision de la transcription",
    ),
    "transform_meetings": MessageLookupByLibrary.simpleMessage(
      "Transformer réunions",
    ),
    "transform_meetings_into_actionable_intelligence":
        MessageLookupByLibrary.simpleMessage("Réunions en actions"),
    "translate_note": MessageLookupByLibrary.simpleMessage("Traduire la Note"),
    "translating_note": MessageLookupByLibrary.simpleMessage(
      "Traduction en Cours...",
    ),
    "translation_completed": MessageLookupByLibrary.simpleMessage(
      "Traduction Terminée",
    ),
    "translation_failed": MessageLookupByLibrary.simpleMessage(
      "Traduction Échouée",
    ),
    "trouble_connecting_to_server": MessageLookupByLibrary.simpleMessage(
      "Nous rencontrons des difficultés pour nous connecter au serveur. Veuillez réessayer dans un instant.",
    ),
    "try_3_day": MessageLookupByLibrary.simpleMessage(
      "Essayez 3 jours gratuits",
    ),
    "try_7_day": MessageLookupByLibrary.simpleMessage(
      "Commencer mon essai de 7 jours",
    ),
    "try_again": MessageLookupByLibrary.simpleMessage(
      "Un problème est survenu lors de la génération de vos notes. Veuillez réessayer !",
    ),
    "try_again_button": MessageLookupByLibrary.simpleMessage("Réessayer"),
    "try_pro_free_7_day": MessageLookupByLibrary.simpleMessage(
      "Essai Pro 7 jours",
    ),
    "type_or_paste_any_text_here": MessageLookupByLibrary.simpleMessage(
      "Tapez ou collez n\'importe quel texte ici. L\'IA le transformera en un résumé clair et structuré avec les points essentiels.",
    ),
    "uidCopied": m3,
    "unable_download_file": MessageLookupByLibrary.simpleMessage(
      "Impossible de télécharger le fichier",
    ),
    "unable_load_audio": MessageLookupByLibrary.simpleMessage(
      "Impossible de charger l\'audio :",
    ),
    "unable_share_audio": MessageLookupByLibrary.simpleMessage(
      "Impossible de partager le fichier audio",
    ),
    "unable_to_connect_to_server": MessageLookupByLibrary.simpleMessage(
      "Assurez-vous que votre téléphone est connecté à internet",
    ),
    "unable_to_extract_web_url": MessageLookupByLibrary.simpleMessage(
      "Impossible d\'extraire le contenu",
    ),
    "unable_to_open_store": MessageLookupByLibrary.simpleMessage(
      "Impossible d\'ouvrir la boutique",
    ),
    "uncover_opportunities": MessageLookupByLibrary.simpleMessage(
      "opportunités",
    ),
    "unknown_error": MessageLookupByLibrary.simpleMessage(
      "L\'application a rencontré une erreur inconnue",
    ),
    "unknown_server_error": MessageLookupByLibrary.simpleMessage(
      "Oups ! Nos serveurs ont trébuché. Veuillez réessayer.",
    ),
    "unlimited_ai_chat": MessageLookupByLibrary.simpleMessage(
      "Chat IA illimité, Carte mentale IA, Fiches, Quiz",
    ),
    "unlimited_ai_chat_ai_mind_map_flashcard_quiz":
        MessageLookupByLibrary.simpleMessage(
          "Chat IA illimité, Carte mentale, Flashcards, Quiz, Partage de notes.",
        ),
    "unlimited_ai_note": MessageLookupByLibrary.simpleMessage(
      "Notes IA illimitées de toutes sources (YouTube, Documents, Enregistrement, Audio)",
    ),
    "unlimited_ai_notes_from_youtube_and_document":
        MessageLookupByLibrary.simpleMessage(
          "Notes IA illimitées à partir d\'enregistrements, de fichiers téléchargés, de liens YouTube.",
        ),
    "unlimited_audio_youtube_website_to_ai_notes":
        MessageLookupByLibrary.simpleMessage(
          "Audio, YouTube, Documents et Sites Web illimités vers Notes IA",
        ),
    "unlimited_everything": MessageLookupByLibrary.simpleMessage(
      "Profitez de notes AI illimitées, d\'un service prioritaire et de fonctionnalités premium",
    ),
    "unlimited_youtube_document_ai_notes": MessageLookupByLibrary.simpleMessage(
      "Notes IA illimitées pour YouTube et Documents",
    ),
    "unlock_all_features": MessageLookupByLibrary.simpleMessage(
      "Débloquer toutes les fonctionnalités",
    ),
    "unlock_essential_life_time": MessageLookupByLibrary.simpleMessage(
      "Accès essentiel à vie",
    ),
    "unlock_lifetime_access": MessageLookupByLibrary.simpleMessage(
      "Débloquez l\'accès à vie",
    ),
    "unlock_pro_lifetime": MessageLookupByLibrary.simpleMessage(
      "Débloquer PRO à vie",
    ),
    "unlock_the_most_ipad": MessageLookupByLibrary.simpleMessage(
      "Débloquez l\'assistant de prise de notes IA le plus puissant",
    ),
    "unlock_the_most_powerful_ai_note_taking_assistant":
        MessageLookupByLibrary.simpleMessage(
          "Débloquez l\'assistant de notes \nIA le plus puissant",
        ),
    "unlock_toge": MessageLookupByLibrary.simpleMessage("DÉBLOQUER ENSEMBLE"),
    "unlock_together": MessageLookupByLibrary.simpleMessage(
      "Débloquer ensemble",
    ),
    "unlock_unlimited_access_to_all_ai_features":
        MessageLookupByLibrary.simpleMessage(
          "Débloquez un accès illimité à toutes les fonctionnalités d\'IA",
        ),
    "unlock_unlimited_ai": MessageLookupByLibrary.simpleMessage(
      "Débloquez l\'expérience IA illimitée",
    ),
    "unsynced_notes": MessageLookupByLibrary.simpleMessage(
      "Notes non synchronisées",
    ),
    "update_available": MessageLookupByLibrary.simpleMessage(
      "Une nouvelle mise à jour est disponible ! Mettez à jour pour une meilleure expérience.",
    ),
    "update_failed": MessageLookupByLibrary.simpleMessage(
      "Échec de la mise à jour des notes communautaires. Veuillez réessayer.",
    ),
    "update_later": MessageLookupByLibrary.simpleMessage("Plus tard"),
    "update_now": MessageLookupByLibrary.simpleMessage("Mettre à jour!"),
    "update_pro": MessageLookupByLibrary.simpleMessage(
      "Passez à l\'expérience Pro",
    ),
    "update_to_pro": MessageLookupByLibrary.simpleMessage("Passer à PRO"),
    "upgrade": MessageLookupByLibrary.simpleMessage("MISE À NIVEAU"),
    "upgrade_now": MessageLookupByLibrary.simpleMessage("Mettre à niveau !"),
    "upgrade_plan": MessageLookupByLibrary.simpleMessage(
      "Améliorer l\'abonnement",
    ),
    "upgrade_to_full_pro_access": MessageLookupByLibrary.simpleMessage(
      "Passer à l\'accès Pro complet",
    ),
    "upgrade_to_pro_tier_at_a_special_price":
        MessageLookupByLibrary.simpleMessage(
          "Passez au niveau Pro à un prix spécial",
        ),
    "upload": MessageLookupByLibrary.simpleMessage("Télécharger"),
    "upload_audio": MessageLookupByLibrary.simpleMessage(
      "Télécharger un audio",
    ),
    "upload_audio_file": MessageLookupByLibrary.simpleMessage(
      "Télécharger un fichier audio",
    ),
    "upload_file": MessageLookupByLibrary.simpleMessage(
      "Télécharger un fichier",
    ),
    "upload_image": MessageLookupByLibrary.simpleMessage(
      "Télécharger une image",
    ),
    "upload_in_progress": MessageLookupByLibrary.simpleMessage(
      "Téléchargement en cours. Gardez l\'écran ouvert.\n Désactivez le VPN pour un téléchargement plus rapide.",
    ),
    "uploading_to_server": MessageLookupByLibrary.simpleMessage(
      "Téléchargement vers un serveur sécurisé",
    ),
    "user_disabled": MessageLookupByLibrary.simpleMessage(
      "L\'utilisateur correspondant à cet e-mail a été désactivé.",
    ),
    "user_not_found": MessageLookupByLibrary.simpleMessage(
      "Informations utilisateur non trouvées.",
    ),
    "verifying_your_credentials": MessageLookupByLibrary.simpleMessage(
      "Vérification de vos informations",
    ),
    "video": MessageLookupByLibrary.simpleMessage("Vidéo"),
    "video_audio": MessageLookupByLibrary.simpleMessage(
      "Enregistrements, vidéos et docs en notes IA",
    ),
    "video_captions": MessageLookupByLibrary.simpleMessage("Sous-titres"),
    "video_is_temporary": MessageLookupByLibrary.simpleMessage(
      "*La vidéo est temporaire - Enregistrez avant de fermer",
    ),
    "visualize_strategies": MessageLookupByLibrary.simpleMessage(
      "Voir stratégies et",
    ),
    "visualize_strategies_opportunities": MessageLookupByLibrary.simpleMessage(
      "Voir stratégies",
    ),
    "visualize_strategies_uncover": MessageLookupByLibrary.simpleMessage(
      "Voir stratégies et",
    ),
    "visualize_strategies_uncover_opportunities":
        MessageLookupByLibrary.simpleMessage("Visualiser les stratégies"),
    "voice": MessageLookupByLibrary.simpleMessage("Voix"),
    "warning_this_ai_note_taking_app_may_cause_excessive_productivity":
        MessageLookupByLibrary.simpleMessage(
          "Attention : Cette app de prise de notes IA peut causer une productivité excessive ! 🚀 Utilisez mon code et nous obtiendrons tous les deux une utilisation supplémentaire. Code : ",
        ),
    "watch_sync_empty_message": MessageLookupByLibrary.simpleMessage(
      "Les enregistrements de votre montre Apple apparaîtront ici",
    ),
    "web": MessageLookupByLibrary.simpleMessage("Web"),
    "web_link": MessageLookupByLibrary.simpleMessage("Lien web"),
    "web_sync": MessageLookupByLibrary.simpleMessage("Synchronisation Web"),
    "website_import": MessageLookupByLibrary.simpleMessage("Import site"),
    "week": MessageLookupByLibrary.simpleMessage("semaine"),
    "week_free_limit": MessageLookupByLibrary.simpleMessage(
      "Limite hebdomadaire atteinte",
    ),
    "weekly": MessageLookupByLibrary.simpleMessage("Hebdomadaire"),
    "weekly_free_limit_reached": MessageLookupByLibrary.simpleMessage(
      "Limite hebdomadaire atteinte",
    ),
    "weekly_free_limit_reached_details": MessageLookupByLibrary.simpleMessage(
      "Vous avez utilisé toutes les transcriptions et résumés AI gratuits pour cette semaine ! Passez à Pro pour un accès illimité ou attendez la réinitialisation de votre quota gratuit.",
    ),
    "welcome_notex": MessageLookupByLibrary.simpleMessage(
      "Bienvenue sur NoteX !",
    ),
    "welcome_title": MessageLookupByLibrary.simpleMessage(
      "Créons votre\npremière note IA",
    ),
    "what_improve": MessageLookupByLibrary.simpleMessage(
      "Ce que vous aimeriez améliorer",
    ),
    "whats_new": MessageLookupByLibrary.simpleMessage("Quoi de neuf"),
    "word_docx": MessageLookupByLibrary.simpleMessage("Word (.docx)"),
    "work_notes_projects": MessageLookupByLibrary.simpleMessage(
      "Notes et projets",
    ),
    "writing_style": MessageLookupByLibrary.simpleMessage("Style d\'Écriture"),
    "wrong": MessageLookupByLibrary.simpleMessage("Faux"),
    "x": MessageLookupByLibrary.simpleMessage("X"),
    "x_skip": MessageLookupByLibrary.simpleMessage("X ?"),
    "year": MessageLookupByLibrary.simpleMessage("année"),
    "yearly": MessageLookupByLibrary.simpleMessage("Annuel"),
    "yes": MessageLookupByLibrary.simpleMessage("Oui"),
    "yesterday": MessageLookupByLibrary.simpleMessage("Hier"),
    "you_are_given_a_special_gift_today": MessageLookupByLibrary.simpleMessage(
      "Vous recevez un cadeau spécial aujourd\'hui 🎁",
    ),
    "you_are_pro": MessageLookupByLibrary.simpleMessage("Accès PRO"),
    "you_can_update_setting": MessageLookupByLibrary.simpleMessage(
      "Vous pouvez mettre à jour à tout moment dans les paramètres.",
    ),
    "you_have_received": MessageLookupByLibrary.simpleMessage("Vous avez reçu"),
    "you_have_received2": MessageLookupByLibrary.simpleMessage(
      "Vous obtiendrez une chance de gagner l\'accès Pro à vie à NoteX ! 3 gagnants tirés au sort le 30 de chaque mois 🎁",
    ),
    "you_will_get_one_entry_to_win_noteX": MessageLookupByLibrary.simpleMessage(
      "Vous obtiendrez une chance de gagner NoteX",
    ),
    "you_will_not_be": MessageLookupByLibrary.simpleMessage(
      "Vous ne pourrez pas le récupérer ensuite",
    ),
    "your_learning": MessageLookupByLibrary.simpleMessage(
      "Boostez votre apprentissage !",
    ),
    "your_learning_device": MessageLookupByLibrary.simpleMessage(
      "Accès Pro NoteX",
    ),
    "your_note_are_ready": MessageLookupByLibrary.simpleMessage(
      "Vos notes sont prêtes.",
    ),
    "your_personal_study": MessageLookupByLibrary.simpleMessage("Votre étude"),
    "your_personal_study_assistant": MessageLookupByLibrary.simpleMessage(
      "Votre assistant d\'études",
    ),
    "your_plan": MessageLookupByLibrary.simpleMessage("Votre forfait"),
    "your_primary": MessageLookupByLibrary.simpleMessage(
      "Quel est votre principal",
    ),
    "your_product": MessageLookupByLibrary.simpleMessage("VOTRE PRODUCTIVITÉ"),
    "your_recording_will_save": MessageLookupByLibrary.simpleMessage(
      "Vos enregistrements seront sauvegardés localement sans transcriptions ni résumés IA. Supprimez les limites pour les traiter après l\'enregistrement.",
    ),
    "your_referrals": MessageLookupByLibrary.simpleMessage("Vos parrainages"),
    "youtube": MessageLookupByLibrary.simpleMessage("YouTube"),
    "youtube_import": MessageLookupByLibrary.simpleMessage(
      "Importation YouTube",
    ),
    "youtube_link": MessageLookupByLibrary.simpleMessage("Lien Youtube"),
    "youtube_transcript_language_guidance": MessageLookupByLibrary.simpleMessage(
      "Sélectionnez la langue de transcription - Cette langue sera utilisée pour générer vos notes IA",
    ),
    "youtube_video": MessageLookupByLibrary.simpleMessage("Vidéo Youtube"),
    "youtube_video_note": MessageLookupByLibrary.simpleMessage("Vidéo YouTube"),
    "yt_credit_err": MessageLookupByLibrary.simpleMessage(
      "Utilisation gratuite YouTube insuffisante. Veuillez mettre à jour votre plan.",
    ),
    "yt_credit_use_err": MessageLookupByLibrary.simpleMessage(
      "Erreur lors de l\'utilisation de la gratuité YouTube. Veuillez réessayer plus tard.",
    ),
    "yt_length_err": MessageLookupByLibrary.simpleMessage(
      "La vidéo YouTube dépasse la limite de 10 heures. Veuillez choisir une vidéo plus courte.",
    ),
    "yt_process_err": MessageLookupByLibrary.simpleMessage(
      "Erreur lors du traitement de la vidéo YouTube. Vérifiez l\'URL et réessayez.",
    ),
    "yt_sum_limit": MessageLookupByLibrary.simpleMessage(
      "Limite de résumé YouTube",
    ),
    "z_to_a": MessageLookupByLibrary.simpleMessage("Z à A"),
  };
}
