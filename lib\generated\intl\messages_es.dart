// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a es locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'es';

  static String m0(date) => "Su prueba vencerá en ${date} días.";

  static String m1(images) => "Se han subido ${images} fotos";

  static String m2(price, date) =>
      "Tu próxima factura de ${price} será el ${date}.";

  static String m3(uid) => "¡ID ${uid} copiado al portapapeles!";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "Regenerate": MessageLookupByLibrary.simpleMessage("Regenerar"),
    "a_to_z": MessageLookupByLibrary.simpleMessage("A-Z"),
    "about_us": MessageLookupByLibrary.simpleMessage("Sobre nosotros"),
    "access_notex_web": MessageLookupByLibrary.simpleMessage(
      "Acceso a NoteX web",
    ),
    "account": MessageLookupByLibrary.simpleMessage("Cuenta"),
    "account_basic": MessageLookupByLibrary.simpleMessage("Básico"),
    "account_content_basic": MessageLookupByLibrary.simpleMessage(
      "Experiencia IA limitada",
    ),
    "account_content_pro": MessageLookupByLibrary.simpleMessage(
      "Desbloquear experiencia IA ilimitada",
    ),
    "account_lifetime": MessageLookupByLibrary.simpleMessage("De por vida"),
    "achieve_more": MessageLookupByLibrary.simpleMessage("LOGRA MÁS"),
    "action_items": MessageLookupByLibrary.simpleMessage("Tareas"),
    "actionable_intelligence": MessageLookupByLibrary.simpleMessage(
      "en acciones",
    ),
    "active_description": MessageLookupByLibrary.simpleMessage(
      "No se encontró una descripción activa.",
    ),
    "active_recall": MessageLookupByLibrary.simpleMessage("recuerdo activo"),
    "add_folder": MessageLookupByLibrary.simpleMessage("Mover a carpeta"),
    "add_note": MessageLookupByLibrary.simpleMessage("Añadir nota"),
    "add_password": MessageLookupByLibrary.simpleMessage("Añadir contraseña"),
    "add_password_to_public": MessageLookupByLibrary.simpleMessage(
      "Añadir contraseña al enlace público",
    ),
    "add_to": MessageLookupByLibrary.simpleMessage("Mover a"),
    "add_to_notes": MessageLookupByLibrary.simpleMessage("Añadir a notas"),
    "additional_ins": MessageLookupByLibrary.simpleMessage(
      "Instrucciones Adicionales (opc.)",
    ),
    "advance_mode": MessageLookupByLibrary.simpleMessage("Modo avanzado"),
    "advanced": MessageLookupByLibrary.simpleMessage("Avanzado"),
    "afternoon_content": MessageLookupByLibrary.simpleMessage(
      "Pequeñas notas, gran impacto",
    ),
    "afternoon_content_1": MessageLookupByLibrary.simpleMessage(
      "Pensamientos capturados, mente libre",
    ),
    "afternoon_content_3": MessageLookupByLibrary.simpleMessage(
      "Orden en medio del caos",
    ),
    "afternoon_content_4": MessageLookupByLibrary.simpleMessage(
      "Tus ideas, organizadas",
    ),
    "afternoon_content_5": MessageLookupByLibrary.simpleMessage(
      "Claridad en progreso",
    ),
    "afternoon_content_6": MessageLookupByLibrary.simpleMessage(
      "Conserva lo que importa",
    ),
    "ai_audio_transcription_per_day": MessageLookupByLibrary.simpleMessage(
      "3 transcripciones AI diarias *",
    ),
    "ai_chat": MessageLookupByLibrary.simpleMessage("Nova IA"),
    "ai_chat_assistant": MessageLookupByLibrary.simpleMessage("Asistente IA"),
    "ai_chat_with_notes": MessageLookupByLibrary.simpleMessage(
      "Chat IA con notas",
    ),
    "ai_insight": MessageLookupByLibrary.simpleMessage("Análisis IA"),
    "ai_learning": MessageLookupByLibrary.simpleMessage("Aprendizaje IA"),
    "ai_learning_companion": MessageLookupByLibrary.simpleMessage(
      "Soy Nova AI de NoteX",
    ),
    "ai_note_create": MessageLookupByLibrary.simpleMessage(
      "Creación de notas con IA",
    ),
    "ai_note_creation": MessageLookupByLibrary.simpleMessage("Notas con IA"),
    "ai_note_from": MessageLookupByLibrary.simpleMessage(
      "Notas IA desde Audio",
    ),
    "ai_notes_10": MessageLookupByLibrary.simpleMessage(
      "Notas IA ilimitadas de YouTube y Documentos",
    ),
    "ai_notes_3": MessageLookupByLibrary.simpleMessage(
      "3 notas de IA al día desde grabaciones y cargas de audio (hasta 60 min por archivo)",
    ),
    "ai_notes_from": MessageLookupByLibrary.simpleMessage(
      "Notas IA desde YouTube, Web, Docs",
    ),
    "ai_short_1": MessageLookupByLibrary.simpleMessage(
      "3 generaciones de videos cortos de IA al día",
    ),
    "ai_short_3": MessageLookupByLibrary.simpleMessage(
      "5 Videos cortos IA por día (beta)",
    ),
    "ai_short_video": MessageLookupByLibrary.simpleMessage("Videos Cortos IA"),
    "ai_study_practice": MessageLookupByLibrary.simpleMessage(
      "Práctica de estudio con IA",
    ),
    "ai_study_tools": MessageLookupByLibrary.simpleMessage(
      "Herramientas de estudio con IA",
    ),
    "ai_summarize": MessageLookupByLibrary.simpleMessage("Resumen con IA"),
    "ai_transcription": MessageLookupByLibrary.simpleMessage(
      "Transcripción IA",
    ),
    "ai_workflow": MessageLookupByLibrary.simpleMessage("Flujo IA"),
    "all": MessageLookupByLibrary.simpleMessage("Todos"),
    "all_note": MessageLookupByLibrary.simpleMessage("Todas las notas"),
    "all_note_in_folder": MessageLookupByLibrary.simpleMessage(
      "¿Estás seguro de que quieres eliminar esta carpeta?",
    ),
    "all_tabs": MessageLookupByLibrary.simpleMessage("Todas las pestañas"),
    "allow": MessageLookupByLibrary.simpleMessage("Permitir"),
    "almost_done": MessageLookupByLibrary.simpleMessage("Casi terminado"),
    "and": MessageLookupByLibrary.simpleMessage("y"),
    "answer": MessageLookupByLibrary.simpleMessage("Respuesta"),
    "anyone_with_link": MessageLookupByLibrary.simpleMessage(
      "Cualquiera con el enlace puede ver",
    ),
    "app_feedback": MessageLookupByLibrary.simpleMessage(
      "Comentarios para la app NoteX",
    ),
    "app_store": MessageLookupByLibrary.simpleMessage("reseña en la App Store"),
    "appearance": MessageLookupByLibrary.simpleMessage("Apariencia"),
    "appreciate_cooperation": MessageLookupByLibrary.simpleMessage(
      "Esta información ayudará a nuestro equipo a identificar y resolver el problema rápidamente. ¡Gracias por tu colaboración!",
    ),
    "appreciate_cooperation2": MessageLookupByLibrary.simpleMessage(
      "Esto nos ayuda a investigar y resolver tu problema de manera más efectiva.",
    ),
    "appreciate_cooperation3": MessageLookupByLibrary.simpleMessage(
      "¡Gracias por usar y confiar en NoteX AI!",
    ),
    "are_you_sure": MessageLookupByLibrary.simpleMessage("Oferta única"),
    "ask_anything": MessageLookupByLibrary.simpleMessage(
      "Pregunta cualquier cosa...",
    ),
    "assist_faster": MessageLookupByLibrary.simpleMessage(
      "Para ayudarnos a asistirte más rápido:",
    ),
    "assistant": MessageLookupByLibrary.simpleMessage("asistente"),
    "at_your_pace": MessageLookupByLibrary.simpleMessage("para sacar un 10"),
    "audio": MessageLookupByLibrary.simpleMessage("Audio"),
    "audio_file": MessageLookupByLibrary.simpleMessage("Archivo de audio"),
    "audio_is_temporary": MessageLookupByLibrary.simpleMessage(
      "*El audio es temporal - Guarda antes de cerrar",
    ),
    "audio_length_err": MessageLookupByLibrary.simpleMessage(
      "El archivo de audio excede la longitud máxima. Sube un archivo más corto.",
    ),
    "audio_length_limit": MessageLookupByLibrary.simpleMessage(
      "Límite de duración de audio",
    ),
    "audio_process_err": MessageLookupByLibrary.simpleMessage(
      "No se pudo procesar el archivo de audio. Inténtalo con un archivo diferente.",
    ),
    "audio_recording_ai_notes_daily": MessageLookupByLibrary.simpleMessage(
      "3 Notas IA de Audio y Grabación Diarias*",
    ),
    "audio_to_ai_note": MessageLookupByLibrary.simpleMessage(
      "De audio a notas con IA",
    ),
    "audio_upload_note": MessageLookupByLibrary.simpleMessage(
      "Subida de audio",
    ),
    "auto": MessageLookupByLibrary.simpleMessage("Auto"),
    "auto_detect": MessageLookupByLibrary.simpleMessage("Detección automática"),
    "auto_generate_slides": MessageLookupByLibrary.simpleMessage(
      "Genera diapositivas atractivas al instante",
    ),
    "auto_renew_after_trial": MessageLookupByLibrary.simpleMessage(
      "Renovación automática después de la prueba • Cancela cuando quieras",
    ),
    "auto_renewable_after_trial": MessageLookupByLibrary.simpleMessage(
      "Renovación automática tras prueba. Cancela en cualquier momento",
    ),
    "auto_renewal": MessageLookupByLibrary.simpleMessage(
      "Renovación automática, cancela en cualquier momento",
    ),
    "available_credits": MessageLookupByLibrary.simpleMessage(
      "Créditos disponibles",
    ),
    "available_transcript": MessageLookupByLibrary.simpleMessage(
      "La transcripción estará disponible después de crear la nota.",
    ),
    "back_content": MessageLookupByLibrary.simpleMessage(" puntos"),
    "background_style": MessageLookupByLibrary.simpleMessage("Estilo de Fondo"),
    "balanced": MessageLookupByLibrary.simpleMessage("Equilibrado"),
    "balanced_description": MessageLookupByLibrary.simpleMessage(
      "Ideas principales con contexto",
    ),
    "basic": MessageLookupByLibrary.simpleMessage("Plan Básico"),
    "basic_features": MessageLookupByLibrary.simpleMessage(
      "Funciones básicas de IA",
    ),
    "beta": MessageLookupByLibrary.simpleMessage("Beta"),
    "between_concepts": MessageLookupByLibrary.simpleMessage("los conceptos"),
    "black_friday_sale": MessageLookupByLibrary.simpleMessage(
      "¡Oferta de Navidad!",
    ),
    "blurred_output_image": MessageLookupByLibrary.simpleMessage(
      "¡Falló la generación de estilo! Elige un estilo diferente o cambia la imagen.",
    ),
    "body_error_document_upload": MessageLookupByLibrary.simpleMessage(
      "Hubo un problema al procesar tu documento. Por favor, ve a la aplicación e inténtalo de nuevo.",
    ),
    "body_error_note_document": MessageLookupByLibrary.simpleMessage(
      "Error procesando el documento. Vuelve a intentarlo en la app.",
    ),
    "body_error_note_recording": MessageLookupByLibrary.simpleMessage(
      "Error procesando la grabación. Vuelve a intentarlo en la app.",
    ),
    "body_error_note_upload": MessageLookupByLibrary.simpleMessage(
      "Error procesando el archivo. Vuelve a intentarlo en la app.",
    ),
    "body_error_note_web": MessageLookupByLibrary.simpleMessage(
      "Hubo un problema al procesar tu enlace web. Por favor, vuelve a la aplicación e inténtalo de nuevo.",
    ),
    "body_error_note_youtube": MessageLookupByLibrary.simpleMessage(
      "Hubo un problema al procesar tu enlace de YouTube. Por favor, vuelve a la aplicación e inténtalo de nuevo.",
    ),
    "body_success_note": MessageLookupByLibrary.simpleMessage(
      "Tu nota AI está lista para revisión.",
    ),
    "bonus_credits_for_new_referred_friends_only":
        MessageLookupByLibrary.simpleMessage(
          "Créditos adicionales para nuevos referidos",
        ),
    "boost_comprehension": MessageLookupByLibrary.simpleMessage(
      "Mejora la comprensión y retención",
    ),
    "boost_comprehension2": MessageLookupByLibrary.simpleMessage(
      "Mejora comprensión",
    ),
    "boost_flashcards_quizzes": MessageLookupByLibrary.simpleMessage(
      "Mejora tu comprensión con tarjetas y cuestionarios generados por IA",
    ),
    "boost_knowledge": MessageLookupByLibrary.simpleMessage(
      "Conecta los puntos",
    ),
    "boost_knowledge_retention": MessageLookupByLibrary.simpleMessage(
      "Conecta los puntos entre",
    ),
    "both_you_friends_receive_usage_credits":
        MessageLookupByLibrary.simpleMessage(
          "Tanto tú como tus amigos recibirán créditos de uso.",
        ),
    "brief_service_disruption": MessageLookupByLibrary.simpleMessage(
      "Tarea no encontrada o ha expirado. Por favor, inténtalo de nuevo. Obtén actualizaciones de estado en Discord.",
    ),
    "business_uses": MessageLookupByLibrary.simpleMessage("Uso empresarial"),
    "button_below": MessageLookupByLibrary.simpleMessage(
      "Pulsa el botón de abajo o elige un tipo de entrada de contenido para empezar",
    ),
    "buy_one_forever": MessageLookupByLibrary.simpleMessage(
      "Compra una vez. Desbloquea Máxima Productividad para Siempre.",
    ),
    "by_subscribing": MessageLookupByLibrary.simpleMessage(
      "Al suscribirte aceptas los",
    ),
    "by_taping_continue": MessageLookupByLibrary.simpleMessage(
      "Al continuar, aceptas",
    ),
    "by_tapping_started": MessageLookupByLibrary.simpleMessage(
      "Al tocar el botón “Comenzar”, aceptas nuestros",
    ),
    "camera": MessageLookupByLibrary.simpleMessage("Cámara"),
    "camera_access": MessageLookupByLibrary.simpleMessage(
      "\"NoteX\" Desea Acceder a la Cámara",
    ),
    "camera_permission": MessageLookupByLibrary.simpleMessage(
      "Acceso a la cámara requerido",
    ),
    "camera_permission_denied_details": MessageLookupByLibrary.simpleMessage(
      "Se requiere acceso a la cámara para seleccionar imágenes. Por favor, conceda el permiso en Configuración.",
    ),
    "can_improve": MessageLookupByLibrary.simpleMessage(
      "¿Qué podemos mejorar?",
    ),
    "cancel": MessageLookupByLibrary.simpleMessage("Cancelar"),
    "cannot_create_pdf_file_from_image": MessageLookupByLibrary.simpleMessage(
      "No se puede crear archivo PDF desde la imagen",
    ),
    "cannot_extract_text_from_pdf": MessageLookupByLibrary.simpleMessage(
      "No se pudo leer el documento. No pudimos extraer texto de este documento. Esto suele suceder con documentos escaneados o PDFs que solo contienen imágenes.",
    ),
    "card": MessageLookupByLibrary.simpleMessage("Tarjeta"),
    "card_count": MessageLookupByLibrary.simpleMessage("Conteo de tarjetas"),
    "card_difficulty": MessageLookupByLibrary.simpleMessage(
      "Dificultad de la tarjeta",
    ),
    "change": MessageLookupByLibrary.simpleMessage("Cambiar"),
    "change_plan": MessageLookupByLibrary.simpleMessage("Cambiar Plan"),
    "chaos_into_clarity": MessageLookupByLibrary.simpleMessage(
      "del caos a la claridad",
    ),
    "characters": MessageLookupByLibrary.simpleMessage("caracteres"),
    "chat_empty": MessageLookupByLibrary.simpleMessage("Chat vacío"),
    "chat_topic_temporary_stored": MessageLookupByLibrary.simpleMessage(
      "Sesión temporal, usa \"Guardar Chat\" para conservar",
    ),
    "check_if_you": MessageLookupByLibrary.simpleMessage(
      "Verifica si estás conectado con la cuenta de Google correcta",
    ),
    "check_update": MessageLookupByLibrary.simpleMessage(
      "Nueva versión disponible",
    ),
    "child_detected": MessageLookupByLibrary.simpleMessage(
      "Se detectó un niño. Sube una imagen diferente.",
    ),
    "choose_your_note": MessageLookupByLibrary.simpleMessage("Elige tu NoteX"),
    "choose_your_note_experience": MessageLookupByLibrary.simpleMessage(
      "Elige tu experiencia NoteX",
    ),
    "click_create_podcast": MessageLookupByLibrary.simpleMessage(
      "Haz clic en \'Crear podcast\' para convertir tu nota en audio atractivo",
    ),
    "click_create_short": MessageLookupByLibrary.simpleMessage(
      "Haz clic en \'Crear Short\' para convertir tu nota en shorts atractivos",
    ),
    "click_create_slide": MessageLookupByLibrary.simpleMessage(
      "Haga clic en \'Crear diapositivas\' para visualizar su nota como una presentación",
    ),
    "click_start_flashcard": MessageLookupByLibrary.simpleMessage(
      "Haz clic en \'Crear tarjetas\' para generar conjuntos de tarjetas basados en la transcripción. Puedes crear varios conjuntos.",
    ),
    "click_start_mindmap": MessageLookupByLibrary.simpleMessage(
      "Haz clic en \'Crear mapa mental\' para generar un mapa mental basado en la transcripción",
    ),
    "click_start_quiz": MessageLookupByLibrary.simpleMessage(
      "Haz clic en \'Crear cuestionarios\' para generar conjuntos de preguntas basados en la transcripción. Puedes crear varios conjuntos.",
    ),
    "click_to_flip": MessageLookupByLibrary.simpleMessage(
      "Haz clic para voltear",
    ),
    "coming_soon": MessageLookupByLibrary.simpleMessage("Próximamente"),
    "community": MessageLookupByLibrary.simpleMessage("Comunidad"),
    "community_feedback": MessageLookupByLibrary.simpleMessage(
      "Comunidad y Retroalimentación",
    ),
    "comprehensive": MessageLookupByLibrary.simpleMessage("Completo"),
    "comprehensive_description": MessageLookupByLibrary.simpleMessage(
      "Cobertura detallada con puntos de apoyo",
    ),
    "congratulations": MessageLookupByLibrary.simpleMessage("¡Felicidades!"),
    "connect_friends": MessageLookupByLibrary.simpleMessage(
      "Importa fácilmente enlaces de notas compartidas de amigos",
    ),
    "connection_fail": MessageLookupByLibrary.simpleMessage(
      "¡Fallo de conexión!",
    ),
    "connection_timeout": MessageLookupByLibrary.simpleMessage(
      "Tiempo de conexión agotado. Compruebe su conexión a internet e inténtelo de nuevo.",
    ),
    "contact_support": MessageLookupByLibrary.simpleMessage("Contacto"),
    "content_account_trial": m0,
    "content_button_flashcard": MessageLookupByLibrary.simpleMessage(
      "Crear tarjetas",
    ),
    "content_button_mindmap": MessageLookupByLibrary.simpleMessage(
      "Crear mapa mental",
    ),
    "content_button_quiz": MessageLookupByLibrary.simpleMessage(
      "Crear cuestionarios",
    ),
    "content_button_summary": MessageLookupByLibrary.simpleMessage(
      "Generar Resumen",
    ),
    "content_camera_access": MessageLookupByLibrary.simpleMessage(
      "NoteX necesita acceso a tu cámara para capturar, reconocer y digitalizar textos de imágenes",
    ),
    "content_delete_note": MessageLookupByLibrary.simpleMessage(
      "No podrás recuperarla después",
    ),
    "content_delete_note_detail": MessageLookupByLibrary.simpleMessage(
      "¿Estás seguro de que quieres eliminar esta nota?",
    ),
    "content_delete_reminder": MessageLookupByLibrary.simpleMessage(
      "¿Estás seguro de que quieres eliminar este recordatorio?",
    ),
    "content_discard_changes": MessageLookupByLibrary.simpleMessage(
      "Salir detendrá la grabación y descartará todos los cambios.",
    ),
    "content_discard_changes_image": MessageLookupByLibrary.simpleMessage(
      "Al cerrar se descartarán las fotos que has capturado",
    ),
    "content_discard_changes_note": MessageLookupByLibrary.simpleMessage(
      "Esta acción descartará todos los cambios sin posibilidad de deshacer.",
    ),
    "content_discard_changes_reminder": MessageLookupByLibrary.simpleMessage(
      "Salir cerrará la notificación del recordatorio y descartará todos los cambios.",
    ),
    "content_empty_flashcard": MessageLookupByLibrary.simpleMessage(
      "El resumen automático aparecerá aquí después de la reunión.",
    ),
    "content_empty_quiz": MessageLookupByLibrary.simpleMessage(
      "El resumen automático aparecerá aquí después de la reunión.",
    ),
    "content_hour": MessageLookupByLibrary.simpleMessage("Horas de contenido"),
    "content_hour_insight": MessageLookupByLibrary.simpleMessage(
      "Horas de contenido en insights",
    ),
    "content_minute_left": MessageLookupByLibrary.simpleMessage(
      "Tus grabaciones se guardarán localmente sin transcripciones ni resúmenes si superan el uso gratuito restante de esta semana. Puedes eliminar todos los límites haciéndote Pro.",
    ),
    "content_payment_successfully": MessageLookupByLibrary.simpleMessage(
      "Gracias por tu compra. Tu transacción se procesó con éxito.",
    ),
    "content_quarter_01": MessageLookupByLibrary.simpleMessage(
      "Notas AI ilimitadas de grabaciones, archivos, enlaces YouTube.",
    ),
    "content_quarter_02": MessageLookupByLibrary.simpleMessage(
      "Chat AI, Mapas Mentales, Tarjetas, Tests, Compartir Notas ilimitados.",
    ),
    "content_save_changes": MessageLookupByLibrary.simpleMessage(
      "Esta acción guardará todos los cambios y se aplicarán permanentemente.",
    ),
    "continue_3_day": MessageLookupByLibrary.simpleMessage(
      "Continuar prueba gratis de 3 días",
    ),
    "continue_button": MessageLookupByLibrary.simpleMessage("Continuar"),
    "continue_with_apple": MessageLookupByLibrary.simpleMessage(
      "Continuar con Apple",
    ),
    "continue_with_email": MessageLookupByLibrary.simpleMessage(
      "Continuar con Email",
    ),
    "continue_with_google": MessageLookupByLibrary.simpleMessage(
      "Continuar con Google",
    ),
    "copied_to_clipboard": MessageLookupByLibrary.simpleMessage(
      "Copiado al portapapeles",
    ),
    "copy": MessageLookupByLibrary.simpleMessage("Copiar"),
    "copy_your_referral_code": MessageLookupByLibrary.simpleMessage(
      "Copia tu código de referido.",
    ),
    "correct": MessageLookupByLibrary.simpleMessage("Correcto"),
    "craft_visual_from_every_note": MessageLookupByLibrary.simpleMessage(
      "Transforma tus notas en diapositivas",
    ),
    "craft_visual_stories": MessageLookupByLibrary.simpleMessage(
      "Transforma tus notas",
    ),
    "create": MessageLookupByLibrary.simpleMessage("Crear"),
    "create_folder": MessageLookupByLibrary.simpleMessage("Crear carpeta"),
    "create_lecture": MessageLookupByLibrary.simpleMessage(
      "Crea resúmenes concisos de tus clases",
    ),
    "create_new_folder": MessageLookupByLibrary.simpleMessage(
      "Crear nueva carpeta",
    ),
    "create_note_successfully": MessageLookupByLibrary.simpleMessage(
      "¡Nota creada exitosamente!",
    ),
    "create_notes": MessageLookupByLibrary.simpleMessage(
      "Creando notas con IA...",
    ),
    "create_podcast": MessageLookupByLibrary.simpleMessage("Crear podcast"),
    "create_reminder": MessageLookupByLibrary.simpleMessage(
      "Crear Recordatorio",
    ),
    "create_select_a_language": MessageLookupByLibrary.simpleMessage(
      "Seleccionar idioma",
    ),
    "create_short": MessageLookupByLibrary.simpleMessage("Crear Short"),
    "create_shorts": MessageLookupByLibrary.simpleMessage("Crear Shorts"),
    "create_slide": MessageLookupByLibrary.simpleMessage("Crear diapositivas"),
    "creating_note": MessageLookupByLibrary.simpleMessage("Creando nota..."),
    "creating_quiz": MessageLookupByLibrary.simpleMessage(
      "Creando preguntas del cuestionario",
    ),
    "credit": MessageLookupByLibrary.simpleMessage("Crédito"),
    "credits": MessageLookupByLibrary.simpleMessage("Créditos"),
    "credits_can_be_used_to_create_notes_and_access_the_features_within_them":
        MessageLookupByLibrary.simpleMessage(
          "Los créditos pueden usarse para crear notas y acceder a las funciones dentro de ellas. Si tu suscripción expira, puedes usar créditos para continuar realizando acciones.",
        ),
    "credits_earned": MessageLookupByLibrary.simpleMessage("Créditos ganados"),
    "credits_premium_features": MessageLookupByLibrary.simpleMessage(
      "créditos y funciones premium.",
    ),
    "credits_used": MessageLookupByLibrary.simpleMessage("Créditos usados"),
    "current_plan": MessageLookupByLibrary.simpleMessage("Plan Actual"),
    "customize_note_tabs": MessageLookupByLibrary.simpleMessage(
      "Personalizar pestañas de notas",
    ),
    "customize_your_note_view": MessageLookupByLibrary.simpleMessage(
      "Personaliza la vista de tus notas",
    ),
    "daily_10": MessageLookupByLibrary.simpleMessage("10 diarios"),
    "daily_3": MessageLookupByLibrary.simpleMessage("3 diarios"),
    "daily_5": MessageLookupByLibrary.simpleMessage("5 diarios"),
    "daily_rewards_limit_reached": MessageLookupByLibrary.simpleMessage(
      "Límite de recompensas diarias alcanzado. ¡Inténtalo mañana!",
    ),
    "daily_shorts_limit_reached": MessageLookupByLibrary.simpleMessage(
      "Límite Diario de Shorts Alcanzado (Beta - Acceso Anticipado)",
    ),
    "daily_shorts_limit_reached_detail": MessageLookupByLibrary.simpleMessage(
      "Has agotado las generaciones de Shorts de hoy. Esta función beta tiene límites diarios para estabilidad. ¡Vuelve mañana para crear más videos cortos con IA!",
    ),
    "daily_slideshow_limit_reached": MessageLookupByLibrary.simpleMessage(
      "Límite de diapositivas diarias alcanzado",
    ),
    "daily_slideshow_limit_reached_detail": MessageLookupByLibrary.simpleMessage(
      "Has utilizado todas las generaciones de diapositivas para hoy. Esta función beta tiene límites diarios para garantizar un rendimiento estable. ¡Vuelve mañana para crear más presentaciones impulsadas por IA!",
    ),
    "dark": MessageLookupByLibrary.simpleMessage("Oscuro"),
    "data": MessageLookupByLibrary.simpleMessage("datos"),
    "day_free_trial_access_all_features": MessageLookupByLibrary.simpleMessage(
      "7-día de prueba gratis para acceder a todas las funciones, entonces solo ",
    ),
    "days": MessageLookupByLibrary.simpleMessage("Días"),
    "db_err": MessageLookupByLibrary.simpleMessage(
      "Error en la base de datos. Por favor, inténtalo más tarde.",
    ),
    "deals_left_at_this_price": MessageLookupByLibrary.simpleMessage(
      "ofertas vitalicias a este precio",
    ),
    "decline_free_trial": MessageLookupByLibrary.simpleMessage(
      "Rechazar prueba gratuita",
    ),
    "default_error": MessageLookupByLibrary.simpleMessage(
      "¡Algo salió mal! Por favor, inténtalo de nuevo.",
    ),
    "delete": MessageLookupByLibrary.simpleMessage("Eliminar"),
    "delete_account": MessageLookupByLibrary.simpleMessage("Eliminar cuenta"),
    "delete_account_detail": MessageLookupByLibrary.simpleMessage(
      "Esta acción no se puede deshacer. Eliminar tu cuenta eliminará de forma permanente: Todas tus notas y grabaciones.",
    ),
    "delete_all_note": MessageLookupByLibrary.simpleMessage(
      "Eliminar todas las notas",
    ),
    "delete_folder": MessageLookupByLibrary.simpleMessage("Eliminar carpeta"),
    "delete_note": MessageLookupByLibrary.simpleMessage("¿Eliminar esta nota?"),
    "delete_note_item": MessageLookupByLibrary.simpleMessage("Eliminar nota"),
    "delete_recording": MessageLookupByLibrary.simpleMessage(
      "Eliminar grabación",
    ),
    "delete_recording_confirmation": MessageLookupByLibrary.simpleMessage(
      "¿Está seguro de que desea eliminar esta grabación",
    ),
    "delete_recording_setting_confirmation": MessageLookupByLibrary.simpleMessage(
      "El archivo de audio se eliminará permanentemente de su dispositivo. Este acción no se puede deshacer.",
    ),
    "delete_reminder": MessageLookupByLibrary.simpleMessage(
      "¿Eliminar recordatorio?",
    ),
    "delete_success": MessageLookupByLibrary.simpleMessage(
      "La nota ha sido eliminada con éxito.",
    ),
    "delete_this_folder": MessageLookupByLibrary.simpleMessage(
      "¿Eliminar esta carpeta?",
    ),
    "delete_this_item": MessageLookupByLibrary.simpleMessage(
      "¿Eliminar este elemento?",
    ),
    "deselect": MessageLookupByLibrary.simpleMessage("Deseleccionar"),
    "detail_unlimited_ai_summaries": MessageLookupByLibrary.simpleMessage(
      "Notas ilimitadas de grabaciones, archivos de audio, documentos y videos de YouTube",
    ),
    "developing_quizzes": MessageLookupByLibrary.simpleMessage(
      "Creando cuestionarios...",
    ),
    "discard": MessageLookupByLibrary.simpleMessage("Descartar"),
    "discard_changes": MessageLookupByLibrary.simpleMessage(
      "¿Descartar cambios?",
    ),
    "dissatisfied": MessageLookupByLibrary.simpleMessage(
      "Gracias por tu opinión. Nos ayudará a mejorar.",
    ),
    "doc": MessageLookupByLibrary.simpleMessage("Documento"),
    "document": MessageLookupByLibrary.simpleMessage(
      "Subir documento (Próximamente)",
    ),
    "document_available": MessageLookupByLibrary.simpleMessage(
      "El documento estará disponible después de crear la nota con éxito!",
    ),
    "document_exceed_limit": MessageLookupByLibrary.simpleMessage(
      "Archivo excede 20MB. Elija uno más pequeño",
    ),
    "document_limit": MessageLookupByLibrary.simpleMessage(
      "Límite de subida de documentos",
    ),
    "document_limit_message": MessageLookupByLibrary.simpleMessage(
      "Los usuarios gratuitos pueden resumir 1 documento por día.",
    ),
    "document_note": MessageLookupByLibrary.simpleMessage("Nota de documento"),
    "document_tab": MessageLookupByLibrary.simpleMessage("Documento"),
    "document_to_ai_note": MessageLookupByLibrary.simpleMessage(
      "De documento a notas con IA",
    ),
    "document_type": MessageLookupByLibrary.simpleMessage(
      "Tipos de archivo admitidos: .pdf, .doc, .docx, .txt, .md",
    ),
    "document_upload_note": MessageLookupByLibrary.simpleMessage(
      "Subida de documento",
    ),
    "document_webview_loading_message": MessageLookupByLibrary.simpleMessage(
      "Cargando contenido del documento...",
    ),
    "done_button_label": MessageLookupByLibrary.simpleMessage("Realizado"),
    "donotallow": MessageLookupByLibrary.simpleMessage("No Permitir"),
    "double_the_benefits": MessageLookupByLibrary.simpleMessage(
      "¡Dobles beneficios!",
    ),
    "download_audio_file": MessageLookupByLibrary.simpleMessage(
      "Descargar audio",
    ),
    "download_sucess": MessageLookupByLibrary.simpleMessage("Descarga exitosa"),
    "duration": MessageLookupByLibrary.simpleMessage("Duración"),
    "each_ai_note_generation_uses_1_credit":
        MessageLookupByLibrary.simpleMessage(
          "Cada generación de nota con IA usa 1 crédito",
        ),
    "each_referral_earns": MessageLookupByLibrary.simpleMessage(
      "Cada referido gana",
    ),
    "early_access": MessageLookupByLibrary.simpleMessage(
      "Acceso Anticipado a Nuevas Funciones",
    ),
    "early_supporters_exclusive_offer": MessageLookupByLibrary.simpleMessage(
      "Oferta exclusiva para apoyadores tempranos",
    ),
    "easily_import_shared_note_link": MessageLookupByLibrary.simpleMessage(
      "Importa fácilmente enlaces de notas compartidas de amigos",
    ),
    "easy": MessageLookupByLibrary.simpleMessage("Fácil"),
    "edit": MessageLookupByLibrary.simpleMessage("Editar"),
    "edit_folder": MessageLookupByLibrary.simpleMessage("Editar carpeta"),
    "edit_folder_name": MessageLookupByLibrary.simpleMessage(
      "Editar nombre de la carpeta",
    ),
    "edit_name": MessageLookupByLibrary.simpleMessage("Editar nombre"),
    "edit_note": MessageLookupByLibrary.simpleMessage("Editar nota"),
    "edit_notes": MessageLookupByLibrary.simpleMessage("Editar nota"),
    "edit_reminder": MessageLookupByLibrary.simpleMessage(
      "Editar Recordatorio",
    ),
    "edit_transcript": MessageLookupByLibrary.simpleMessage(
      "Editar transcripción",
    ),
    "edit_transcript_json_fail": MessageLookupByLibrary.simpleMessage(
      "Fallo al editar transcripción. Intenta de nuevo.",
    ),
    "edit_transcript_json_success": MessageLookupByLibrary.simpleMessage(
      "Transcripción con marca de tiempo editada con éxito",
    ),
    "email_invalid": MessageLookupByLibrary.simpleMessage(
      "El correo no es válido.",
    ),
    "email_sent": MessageLookupByLibrary.simpleMessage("Revisa tu bandeja"),
    "email_sent_success": MessageLookupByLibrary.simpleMessage(
      "Te enviamos un enlace mágico para iniciar sesión. Haz clic en el enlace del email para continuar.",
    ),
    "enable_free": MessageLookupByLibrary.simpleMessage(
      "Activar prueba gratuita",
    ),
    "enables_swap": MessageLookupByLibrary.simpleMessage(
      "Permite reordenar imágenes mediante selección e intercambio",
    ),
    "english": MessageLookupByLibrary.simpleMessage("Inglés"),
    "enter_card_count": MessageLookupByLibrary.simpleMessage(
      "Introduce la cantidad de tarjetas",
    ),
    "enter_email": MessageLookupByLibrary.simpleMessage("Ingresa tu correo..."),
    "enter_feedback": MessageLookupByLibrary.simpleMessage(
      "Introduce tu opinión",
    ),
    "enter_folder_name": MessageLookupByLibrary.simpleMessage(
      "Introduce el nombre de la carpeta",
    ),
    "enter_new_name": MessageLookupByLibrary.simpleMessage(
      "Ingresa un nuevo nombre",
    ),
    "enter_quiz_count": MessageLookupByLibrary.simpleMessage(
      "Introduce la cantidad de cuestionarios",
    ),
    "enter_referral_code": MessageLookupByLibrary.simpleMessage(
      "Ingresa código de referido",
    ),
    "enter_slide_count": MessageLookupByLibrary.simpleMessage(
      "Introduzca el número de diapositivas",
    ),
    "enter_title": MessageLookupByLibrary.simpleMessage("Introduce un título"),
    "enter_valid_email": MessageLookupByLibrary.simpleMessage(
      "Ingresa un correo válido",
    ),
    "error": MessageLookupByLibrary.simpleMessage("Error"),
    "error_connection": MessageLookupByLibrary.simpleMessage(
      "Hubo un problema con la conexión.\nInténtalo de nuevo",
    ),
    "error_convert_image": MessageLookupByLibrary.simpleMessage(
      "Error al convertir ui.Image a image.Image",
    ),
    "error_logging_in": MessageLookupByLibrary.simpleMessage(
      "Conectar a internet",
    ),
    "esc": MessageLookupByLibrary.simpleMessage("Esc"),
    "essential": MessageLookupByLibrary.simpleMessage("Essential"),
    "essential_lifetime": MessageLookupByLibrary.simpleMessage(
      "Esencial de por Vida",
    ),
    "essential_lifetime_access": MessageLookupByLibrary.simpleMessage(
      "Essential Lifetime Access",
    ),
    "evening_content": MessageLookupByLibrary.simpleMessage(
      "Reflexiona, captura, crece",
    ),
    "evening_content_2": MessageLookupByLibrary.simpleMessage(
      "Insights de hoy preservados",
    ),
    "evening_content_3": MessageLookupByLibrary.simpleMessage(
      "El mañana comienza con las notas de hoy",
    ),
    "evening_content_4": MessageLookupByLibrary.simpleMessage(
      "Pensamientos organizados, mente tranquila",
    ),
    "evening_content_5": MessageLookupByLibrary.simpleMessage(
      "Guarda ahora, agradécete después",
    ),
    "evening_content_6": MessageLookupByLibrary.simpleMessage(
      "Progreso preservado",
    ),
    "every_note_you_take": MessageLookupByLibrary.simpleMessage(
      "en diapositivas",
    ),
    "experience": MessageLookupByLibrary.simpleMessage("experiencia"),
    "export": MessageLookupByLibrary.simpleMessage("Exportar"),
    "export_as": MessageLookupByLibrary.simpleMessage("Exportar como"),
    "export_audio": MessageLookupByLibrary.simpleMessage(
      "Exportar archivo de audio",
    ),
    "export_failed": MessageLookupByLibrary.simpleMessage(
      "Exportación fallida. Inténtelo de nuevo más tarde.",
    ),
    "export_flashcard": MessageLookupByLibrary.simpleMessage(
      "Exportar Tarjeta",
    ),
    "export_mind_map": MessageLookupByLibrary.simpleMessage(
      "Exportar mapa mental como",
    ),
    "export_pdf": MessageLookupByLibrary.simpleMessage("Exportar resumen"),
    "export_quiz": MessageLookupByLibrary.simpleMessage(
      "Exportar Cuestionario",
    ),
    "export_to_pdf_share_notes": MessageLookupByLibrary.simpleMessage(
      "Exportar a PDF y Compartir Notas",
    ),
    "export_transcript": MessageLookupByLibrary.simpleMessage(
      "Exportar Transcripción",
    ),
    "extracting_text_from_document": MessageLookupByLibrary.simpleMessage(
      "Extrayendo texto del documento",
    ),
    "fail": MessageLookupByLibrary.simpleMessage("Fallo"),
    "fail_create_pdf": MessageLookupByLibrary.simpleMessage(
      "Error al crear archivo PDF",
    ),
    "fail_to_load_document": MessageLookupByLibrary.simpleMessage(
      "¡Error al cargar el documento!",
    ),
    "fail_to_load_video": MessageLookupByLibrary.simpleMessage(
      "Error al cargar video",
    ),
    "failed_get_anonymous_user": MessageLookupByLibrary.simpleMessage(
      "No se pudo obtener el usuario anónimo JWT.",
    ),
    "failed_to_delete_recording": MessageLookupByLibrary.simpleMessage(
      "Error al eliminar la grabación",
    ),
    "failed_to_load_slideshow": MessageLookupByLibrary.simpleMessage(
      "No se pudo cargar el conjunto de diapositivas del sistema. Intenta de nuevo para una mejor experiencia.",
    ),
    "failed_to_save_file": MessageLookupByLibrary.simpleMessage(
      "Error al guardar el archivo",
    ),
    "feedback": MessageLookupByLibrary.simpleMessage("Comentarios"),
    "file_import": MessageLookupByLibrary.simpleMessage("Importar archivo"),
    "file_save_success": MessageLookupByLibrary.simpleMessage(
      "Archivo guardado con éxito",
    ),
    "file_size_err": MessageLookupByLibrary.simpleMessage(
      "El tamaño del archivo excede el límite. Sube un archivo más pequeño.",
    ),
    "filter": MessageLookupByLibrary.simpleMessage("Filtrar"),
    "filter_and_sort": MessageLookupByLibrary.simpleMessage(
      "Filtrar y ordenar",
    ),
    "finalizing": MessageLookupByLibrary.simpleMessage("Finalizando..."),
    "find_and_replace": MessageLookupByLibrary.simpleMessage(
      "Buscar y Reemplazar",
    ),
    "flash_card_gen_success": MessageLookupByLibrary.simpleMessage(
      "Tarjetas generadas con éxito",
    ),
    "flash_card_iap": MessageLookupByLibrary.simpleMessage(
      "Conjunto de tarjetas",
    ),
    "flashcard": MessageLookupByLibrary.simpleMessage("Tarjeta"),
    "flashcard_set_not_found": MessageLookupByLibrary.simpleMessage(
      "Conjunto de tarjetas didácticas no encontrado",
    ),
    "flashcard_sets": MessageLookupByLibrary.simpleMessage(
      "Conjuntos de tarjetas",
    ),
    "flashcards": MessageLookupByLibrary.simpleMessage("Tarjetas"),
    "flashcards_for": MessageLookupByLibrary.simpleMessage("Tarjetas para"),
    "focus_on": MessageLookupByLibrary.simpleMessage("Enfócate en lo Esencial"),
    "folder": MessageLookupByLibrary.simpleMessage("Carpetas"),
    "follow_steps_to_get_rewarded": MessageLookupByLibrary.simpleMessage(
      "Sigue estos pasos para obtener recompensas",
    ),
    "for_unlimited_experiences": MessageLookupByLibrary.simpleMessage(
      "para experiencias ilimitadas.",
    ),
    "free": MessageLookupByLibrary.simpleMessage("Gratis"),
    "free_30_minutes": MessageLookupByLibrary.simpleMessage(
      "Gratis: 30 minutos por archivo",
    ),
    "free_messages": MessageLookupByLibrary.simpleMessage("mensajes gratis"),
    "free_recording_limit": MessageLookupByLibrary.simpleMessage(
      "Límite de grabación gratuita",
    ),
    "free_recording_limit_details": MessageLookupByLibrary.simpleMessage(
      "Te quedan %s minutos de transcripciones y resúmenes gratuitos esta semana.",
    ),
    "free_trial": MessageLookupByLibrary.simpleMessage("Prueba gratuita"),
    "free_updates": MessageLookupByLibrary.simpleMessage(
      "Actualizaciones y mejoras gratuitas de por vida",
    ),
    "free_usage": MessageLookupByLibrary.simpleMessage("Uso Gratis - Semanal"),
    "free_user_audio": MessageLookupByLibrary.simpleMessage(
      "Usuarios gratis: hasta 30 minutos de audio",
    ),
    "free_user_can": MessageLookupByLibrary.simpleMessage(
      "Los usuarios gratuitos pueden resumir 1 video de YouTube (máx. 30 min) al día.",
    ),
    "friendly": MessageLookupByLibrary.simpleMessage("Amigable"),
    "friendly_description": MessageLookupByLibrary.simpleMessage(
      "Conversacional con emojis",
    ),
    "front_content": MessageLookupByLibrary.simpleMessage("Has obtenido "),
    "future_features": MessageLookupByLibrary.simpleMessage(
      "Funciones futuras podrían tener límites",
    ),
    "gen_ai": MessageLookupByLibrary.simpleMessage("Generando IA..."),
    "gen_ai_voice": MessageLookupByLibrary.simpleMessage("Generando voz de IA"),
    "gen_quiz_bgr": MessageLookupByLibrary.simpleMessage(
      "Generando fondo del cuestionario",
    ),
    "generate_audio": MessageLookupByLibrary.simpleMessage("Generar Audio"),
    "generate_content": MessageLookupByLibrary.simpleMessage(
      "Genera resúmenes inteligentes de contenido de YouTube",
    ),
    "generate_note_fail": MessageLookupByLibrary.simpleMessage(
      "¡Error al generar notas con IA!",
    ),
    "generate_shorts_step_1": MessageLookupByLibrary.simpleMessage(
      "Creando tu historia...",
    ),
    "generate_shorts_step_2": MessageLookupByLibrary.simpleMessage(
      "Añadiendo la voz perfecta...",
    ),
    "generate_shorts_step_3": MessageLookupByLibrary.simpleMessage(
      "¡Haciéndolo increíble! Este video valdrá la pena compartir #NoteXAI",
    ),
    "generate_shorts_study_guides": MessageLookupByLibrary.simpleMessage(
      "Generar Shorts y Guías de Estudio",
    ),
    "generate_transcript_notes": MessageLookupByLibrary.simpleMessage(
      "Generaremos una transcripción, notas y una guía de estudio",
    ),
    "generate_video": MessageLookupByLibrary.simpleMessage("Generar Video"),
    "generating_ai_note": MessageLookupByLibrary.simpleMessage(
      "Generando nota AI",
    ),
    "generating_summary": MessageLookupByLibrary.simpleMessage(
      "Generando resumen con IA...",
    ),
    "get_fail": MessageLookupByLibrary.simpleMessage(
      "Error al obtener cuestionarios/tarjetas/mapas mentales. ¡Inténtalo de nuevo!",
    ),
    "get_more_done": MessageLookupByLibrary.simpleMessage("Logra más"),
    "get_more_done_stay_on_track": MessageLookupByLibrary.simpleMessage(
      "Logra más, mantén el rumbo",
    ),
    "get_now": MessageLookupByLibrary.simpleMessage("OBTENER AHORA"),
    "get_offer_now": MessageLookupByLibrary.simpleMessage(
      "Obtener oferta ahora",
    ),
    "get_start": MessageLookupByLibrary.simpleMessage("Comenzar"),
    "go_back": MessageLookupByLibrary.simpleMessage("Regresar"),
    "go_email": MessageLookupByLibrary.simpleMessage("Ir al correo"),
    "go_pro": MessageLookupByLibrary.simpleMessage("Hazte PRO"),
    "go_unlimited": MessageLookupByLibrary.simpleMessage("¡Desbloquea todo!"),
    "good_afternoon": MessageLookupByLibrary.simpleMessage("¡Buenas tardes!"),
    "good_evening": MessageLookupByLibrary.simpleMessage("¡Buenas noches!"),
    "good_morning": MessageLookupByLibrary.simpleMessage("¡Buenos días!"),
    "got_it": MessageLookupByLibrary.simpleMessage("¡Entendido!"),
    "hard": MessageLookupByLibrary.simpleMessage("Difícil"),
    "hello_welcome": MessageLookupByLibrary.simpleMessage(
      "Bienvenido de nuevo 👋",
    ),
    "help_legal": MessageLookupByLibrary.simpleMessage("Ayuda y legal"),
    "help_us_grow": MessageLookupByLibrary.simpleMessage("¡Ayúdanos a crecer!"),
    "hi": MessageLookupByLibrary.simpleMessage("Hola"),
    "hope_enjoy_app": MessageLookupByLibrary.simpleMessage(
      "¡Esperamos que disfrutes de nuestra app y gracias por tu apoyo!",
    ),
    "hours": MessageLookupByLibrary.simpleMessage("Horas"),
    "how_will_you_use_notex": MessageLookupByLibrary.simpleMessage(
      "¿Cómo usarás NoteX?",
    ),
    "http_failed": MessageLookupByLibrary.simpleMessage(
      "Solicitud HTTP fallida. Inténtelo de nuevo más tarde.",
    ),
    "idea1": MessageLookupByLibrary.simpleMessage(
      "Inicia sesión con tu cuenta de Google o Apple si aún no lo has hecho.",
    ),
    "idea2": MessageLookupByLibrary.simpleMessage(
      "Proporciona una breve descripción de lo ocurrido.",
    ),
    "idea3": MessageLookupByLibrary.simpleMessage(
      "Incluye detalles relevantes (dispositivo, versión del sistema operativo).",
    ),
    "idea4": MessageLookupByLibrary.simpleMessage(
      "Menciona cuándo comenzó a ocurrir el problema.",
    ),
    "idea5": MessageLookupByLibrary.simpleMessage(
      "Envíanos un correo directamente a",
    ),
    "image": MessageLookupByLibrary.simpleMessage("Imagen"),
    "image_jpeg": MessageLookupByLibrary.simpleMessage("Imagen (.jpeg)"),
    "image_png": MessageLookupByLibrary.simpleMessage("Imagen (.png)"),
    "image_quality_too_low": MessageLookupByLibrary.simpleMessage(
      "Calidad de imagen demasiado baja. Usa una imagen de mejor calidad.",
    ),
    "image_too_large": MessageLookupByLibrary.simpleMessage(
      "Imagen demasiado grande. Sube una de menos de 10 MB.",
    ),
    "images_have_been_uploaded": m1,
    "import_note_links": MessageLookupByLibrary.simpleMessage(
      "Importar enlaces de notas",
    ),
    "import_notes": MessageLookupByLibrary.simpleMessage(
      "Importar Notas Compartidas",
    ),
    "improve_responses": MessageLookupByLibrary.simpleMessage(
      "Tus respuestas nos ayudarán a mejorar",
    ),
    "initializing_camera": MessageLookupByLibrary.simpleMessage(
      "Inicializando cámara...",
    ),
    "insight_instantly": MessageLookupByLibrary.simpleMessage(
      "Horas de contenido en insights al instante",
    ),
    "insights_instantly": MessageLookupByLibrary.simpleMessage(
      "insights al instante",
    ),
    "instant_answers_from_your": MessageLookupByLibrary.simpleMessage(
      "Respuestas de tus",
    ),
    "instant_answers_from_your_meeting_data":
        MessageLookupByLibrary.simpleMessage("Respuestas de reuniones"),
    "instant_answers_meeting": MessageLookupByLibrary.simpleMessage(
      "Respuestas de reunión",
    ),
    "instantly": MessageLookupByLibrary.simpleMessage("al instante"),
    "interactive_ai_flashcards": MessageLookupByLibrary.simpleMessage(
      "Mapas mentales interactivos con IA",
    ),
    "interactive_flash": MessageLookupByLibrary.simpleMessage(
      "Tarjetas interactivas",
    ),
    "interactive_flashcards": MessageLookupByLibrary.simpleMessage(
      "Tarjetas y mapas mentales ilimitados",
    ),
    "interactive_flashcards_quiz": MessageLookupByLibrary.simpleMessage(
      "Tarjetas y cuestionarios",
    ),
    "introduce_guidance": MessageLookupByLibrary.simpleMessage(
      "Gracias por contactarnos. Para ayudarnos a investigar y resolver tu problema, sigue estos pasos:",
    ),
    "introduce_guidance2": MessageLookupByLibrary.simpleMessage(
      "Entendemos lo frustrante que puede ser tener problemas, especialmente si involucran tus notas o grabaciones importantes. Nuestro equipo de soporte está listo para resolver cualquier problema, generalmente en menos de 12 horas.",
    ),
    "inv_audio": MessageLookupByLibrary.simpleMessage(
      "Archivo de audio inválido. Sube un formato compatible.",
    ),
    "inv_yt_url": MessageLookupByLibrary.simpleMessage(
      "URL de YouTube inválida. Proporciona un enlace válido.",
    ),
    "invalid_code": MessageLookupByLibrary.simpleMessage(
      "Código inválido. Inténtalo de nuevo.",
    ),
    "invalid_file_type": MessageLookupByLibrary.simpleMessage(
      "El archivo subido tiene un formato incorrecto. Por favor, súbelo de nuevo.",
    ),
    "invalid_token": MessageLookupByLibrary.simpleMessage("Token inválido"),
    "invite_friends": MessageLookupByLibrary.simpleMessage(
      "Invita amigos - ambos desbloquean extra",
    ),
    "join_discord": MessageLookupByLibrary.simpleMessage("Únete a Discord"),
    "join_noteX_ai_lets_level_up_together":
        MessageLookupByLibrary.simpleMessage(
          "¡Únete a NoteX AI y subamos de nivel juntos!",
        ),
    "language": MessageLookupByLibrary.simpleMessage("Idioma"),
    "language_tip": MessageLookupByLibrary.simpleMessage(
      "Elige el idioma principal para mejores resultados",
    ),
    "language_tip_1": MessageLookupByLibrary.simpleMessage(
      "Selecciona idioma para mejores resultados",
    ),
    "language_tip_2": MessageLookupByLibrary.simpleMessage(
      "Para conversaciones mixtas, elige multi-idioma",
    ),
    "language_tip_3": MessageLookupByLibrary.simpleMessage(
      "Las llamadas pausarán la grabación. Reanúdala en la app",
    ),
    "latest_ai_models": MessageLookupByLibrary.simpleMessage(
      "Últimos modelos de IA",
    ),
    "learn_faster_through": MessageLookupByLibrary.simpleMessage(
      "Aprende más rápido",
    ),
    "learn_faster_through_active_recall": MessageLookupByLibrary.simpleMessage(
      "Aprende más rápido con recuerdo activo",
    ),
    "learn_smart": MessageLookupByLibrary.simpleMessage(
      "Aprende Inteligentemente",
    ),
    "learn_unlimited": MessageLookupByLibrary.simpleMessage(
      "¡Aprende con Go Unlimited!",
    ),
    "lecture_notes_study_materials": MessageLookupByLibrary.simpleMessage(
      "Apuntes y materiales",
    ),
    "let_ai_handle": MessageLookupByLibrary.simpleMessage(
      "Deja que la IA maneje los detalles",
    ),
    "let_note_ai": MessageLookupByLibrary.simpleMessage(
      "Deja que NoteX IA convierta la información",
    ),
    "let_start": MessageLookupByLibrary.simpleMessage("¡Empecemos!"),
    "lets_create_your_first_ai_note": MessageLookupByLibrary.simpleMessage(
      "¡Vamos a crear tu primera nota de IA!",
    ),
    "lifetime": MessageLookupByLibrary.simpleMessage("Plan Vitalicio"),
    "lifetime_pro_access_level_up_together":
        MessageLookupByLibrary.simpleMessage(
          "¡Acceso Pro de por vida! Subamos de nivel juntos ✨",
        ),
    "lifetime_setting": MessageLookupByLibrary.simpleMessage("de por vida"),
    "lifetime_spots_remaining": MessageLookupByLibrary.simpleMessage(
      "Plazas limitadas disponibles",
    ),
    "light": MessageLookupByLibrary.simpleMessage("Claro"),
    "limited_notes": MessageLookupByLibrary.simpleMessage(
      "Notas limitadas por día",
    ),
    "limited_offer": MessageLookupByLibrary.simpleMessage("Oferta Limitada"),
    "limited_time": MessageLookupByLibrary.simpleMessage("Tiempo limitado"),
    "link": MessageLookupByLibrary.simpleMessage("Enlace"),
    "link_error": MessageLookupByLibrary.simpleMessage("Error con el enlace"),
    "link_expired": MessageLookupByLibrary.simpleMessage(
      "El enlace del email ha expirado.",
    ),
    "link_invalid": MessageLookupByLibrary.simpleMessage(
      "El enlace es inválido, pudo haber expirado o ya fue usado. Solicita uno nuevo.",
    ),
    "loading": MessageLookupByLibrary.simpleMessage("Cargando"),
    "loading_content": MessageLookupByLibrary.simpleMessage(
      "Cargando contenido...",
    ),
    "local_recording": MessageLookupByLibrary.simpleMessage("Grabación Smart"),
    "login_failed": MessageLookupByLibrary.simpleMessage(
      "Fallo al iniciar sesión.",
    ),
    "login_info_1": MessageLookupByLibrary.simpleMessage(
      "Accede a tus notas desde cualquier dispositivo",
    ),
    "login_info_2": MessageLookupByLibrary.simpleMessage(
      "Seguridad empresarial potenciada por AWS",
    ),
    "login_info_3": MessageLookupByLibrary.simpleMessage(
      "Tus datos permanecen privados",
    ),
    "login_info_4": MessageLookupByLibrary.simpleMessage(
      "Accede en Web en notexapp.com",
    ),
    "login_success": MessageLookupByLibrary.simpleMessage(
      "¡Inicio de sesión exitoso!",
    ),
    "login_title": MessageLookupByLibrary.simpleMessage(
      "¡Maximiza la Productividad, en Todas Partes!",
    ),
    "login_title_2": MessageLookupByLibrary.simpleMessage(
      "Presentamos NoteX 2.0",
    ),
    "login_unsuccessful": MessageLookupByLibrary.simpleMessage(
      "Inicio fallido. Intente de nuevo o use otro método de acceso",
    ),
    "logout": MessageLookupByLibrary.simpleMessage("Cerrar sesión"),
    "logout_detail": MessageLookupByLibrary.simpleMessage(
      "¿Estás seguro de que quieres cerrar sesión?",
    ),
    "logout_question_mark": MessageLookupByLibrary.simpleMessage(
      "¿Cerrar sesión?",
    ),
    "loved_by": MessageLookupByLibrary.simpleMessage("Amado por"),
    "make_the_interface_feel_more_like_you": MessageLookupByLibrary.simpleMessage(
      "Haz que la interfaz se sienta más como tú — con preferencias de tema, fuente y diseño al alcance de tu mano.",
    ),
    "making_amazing": MessageLookupByLibrary.simpleMessage(
      "¡Haciéndolo increíble! Este video de cuestionario valdrá la pena compartirlo #NoteXAI",
    ),
    "manage_recordings": MessageLookupByLibrary.simpleMessage(
      "Administrar grabaciones",
    ),
    "map_all_together": MessageLookupByLibrary.simpleMessage(
      "Mapeando todo junto",
    ),
    "markdown_md": MessageLookupByLibrary.simpleMessage("Markdown (.md)"),
    "max_30_cards_per_set": MessageLookupByLibrary.simpleMessage(
      "Máximo 30 tarjetas por conjunto",
    ),
    "max_30_quiz_sets": MessageLookupByLibrary.simpleMessage(
      "Máximo de 30 preguntas por conjunto",
    ),
    "max_3_flashcard_sets": MessageLookupByLibrary.simpleMessage(
      "Máximo 3 conjuntos de tarjetas",
    ),
    "max_3_quiz_sets": MessageLookupByLibrary.simpleMessage(
      "Máximo de 3 conjuntos de cuestionarios",
    ),
    "max_60_min_per_file": MessageLookupByLibrary.simpleMessage(
      "* máx. 60 min por archivo",
    ),
    "max_ai": MessageLookupByLibrary.simpleMessage(
      "Máxima transcripción con IA:",
    ),
    "maybe_later": MessageLookupByLibrary.simpleMessage("Quizás después"),
    "medium": MessageLookupByLibrary.simpleMessage("Medio"),
    "meeting_data": MessageLookupByLibrary.simpleMessage("datos reunión"),
    "migrating_your_notes": MessageLookupByLibrary.simpleMessage(
      "Migrando tus notas...",
    ),
    "migration_complete": MessageLookupByLibrary.simpleMessage(
      "¡Migración completa!",
    ),
    "mind_map": MessageLookupByLibrary.simpleMessage("Mapa mental"),
    "mind_map_gen_success": MessageLookupByLibrary.simpleMessage(
      "Mapa mental generado con éxito",
    ),
    "mind_map_iap": MessageLookupByLibrary.simpleMessage("Mapa mental"),
    "mind_map_study": MessageLookupByLibrary.simpleMessage(
      "Mapas Mentales, Guías de Estudio",
    ),
    "minute_60_per_file": MessageLookupByLibrary.simpleMessage(
      "Esencial: 60 min por archivo",
    ),
    "minute_free": MessageLookupByLibrary.simpleMessage(
      "Has usado los 30 minutos gratis de transcripciones y resúmenes de IA esta semana. Mejora a Pro para acceso ilimitado o espera hasta la próxima semana.",
    ),
    "minutes": MessageLookupByLibrary.simpleMessage("Minutos"),
    "minutes_free_left": MessageLookupByLibrary.simpleMessage(
      " Minutos gratis restantes",
    ),
    "minutes_remaining": MessageLookupByLibrary.simpleMessage(
      "minutos restantes",
    ),
    "mixed": MessageLookupByLibrary.simpleMessage("Mixto"),
    "month": MessageLookupByLibrary.simpleMessage("mes"),
    "monthly": MessageLookupByLibrary.simpleMessage("Mensual"),
    "more_summarize": MessageLookupByLibrary.simpleMessage(
      "Resume reuniones, podcasts, tutoriales y más",
    ),
    "morning_content": MessageLookupByLibrary.simpleMessage(
      "Captura la brillantez de hoy",
    ),
    "morning_content_2": MessageLookupByLibrary.simpleMessage(
      "Mente clara, camino claro",
    ),
    "morning_content_3": MessageLookupByLibrary.simpleMessage(
      "Las notas de hoy dan forma al mañana",
    ),
    "morning_content_4": MessageLookupByLibrary.simpleMessage(
      "Primer pensamiento, mejor pensamiento",
    ),
    "morning_content_5": MessageLookupByLibrary.simpleMessage(
      "Comienza con claridad",
    ),
    "morning_content_6": MessageLookupByLibrary.simpleMessage(
      "Ideas que vale la pena guardar",
    ),
    "most_popular": MessageLookupByLibrary.simpleMessage("Más popular"),
    "multi_language": MessageLookupByLibrary.simpleMessage("Multiidioma"),
    "multiple_people_detected": MessageLookupByLibrary.simpleMessage(
      "Se detectaron varias personas. Sube una imagen de una sola persona.",
    ),
    "multiply_knowledge_with_friends": MessageLookupByLibrary.simpleMessage(
      "Multiplica conocimiento",
    ),
    "my_notes": MessageLookupByLibrary.simpleMessage("Mis notas"),
    "name": MessageLookupByLibrary.simpleMessage("Nombre"),
    "network_error": MessageLookupByLibrary.simpleMessage(
      "Error de red. Revisa tu conexión y vuelve a intentarlo.",
    ),
    "neutral": MessageLookupByLibrary.simpleMessage("Neutral"),
    "neutral_description": MessageLookupByLibrary.simpleMessage(
      "Presentación directa y objetiva",
    ),
    "new_new": MessageLookupByLibrary.simpleMessage("Nuevo"),
    "new_note": MessageLookupByLibrary.simpleMessage("Nueva nota"),
    "new_recording": MessageLookupByLibrary.simpleMessage("Nueva grabación - "),
    "newest_first": MessageLookupByLibrary.simpleMessage("Más reciente"),
    "next_bill_date": m2,
    "no": MessageLookupByLibrary.simpleMessage("No"),
    "no_generated": MessageLookupByLibrary.simpleMessage(
      "Sin cuestionarios/tarjetas generados. ¡Toca para crear ahora!",
    ),
    "no_input": MessageLookupByLibrary.simpleMessage(
      "No se proporcionó entrada. Sube un archivo de audio o introduce una URL de YouTube.",
    ),
    "no_internet": MessageLookupByLibrary.simpleMessage(
      "Sin conexión a internet",
    ),
    "no_internet_connection": MessageLookupByLibrary.simpleMessage(
      "¡Sin conexión a Internet!",
    ),
    "no_notes_found": MessageLookupByLibrary.simpleMessage(
      "No se encontraron notas con este filtro.\nPor favor, restablece la selección del filtro.",
    ),
    "no_notes_in_folder": MessageLookupByLibrary.simpleMessage(
      "No hay notas en esta carpeta.",
    ),
    "no_payment_now": MessageLookupByLibrary.simpleMessage("✓ Sin pago ahora"),
    "no_person_detected": MessageLookupByLibrary.simpleMessage(
      "No se detectó persona. Sube una imagen con una persona.",
    ),
    "no_recording_credit": MessageLookupByLibrary.simpleMessage(
      "Uso de grabación insuficiente. Mejora tu plan.",
    ),
    "no_recordings": MessageLookupByLibrary.simpleMessage("No hay grabaciones"),
    "no_results_found": MessageLookupByLibrary.simpleMessage(
      "No se encontraron resultados para",
    ),
    "no_speech_detected": MessageLookupByLibrary.simpleMessage(
      "No se detectó voz",
    ),
    "no_summary": MessageLookupByLibrary.simpleMessage(
      "No hay resumen disponible para esta nota.",
    ),
    "no_transcript": MessageLookupByLibrary.simpleMessage(
      "No hay transcripción disponible para esta nota.",
    ),
    "no_upload_credit": MessageLookupByLibrary.simpleMessage(
      "Uso de subida insuficiente. Mejora tu plan.",
    ),
    "no_url_provided": MessageLookupByLibrary.simpleMessage(
      "No se proporcionó URL de exportación.",
    ),
    "no_voice_available": MessageLookupByLibrary.simpleMessage(
      "Sin voz disponible",
    ),
    "not_found_audio": MessageLookupByLibrary.simpleMessage(
      "Archivo de audio no encontrado",
    ),
    "not_open_mail": MessageLookupByLibrary.simpleMessage(
      "¡No se pudo abrir el correo!",
    ),
    "not_open_web": MessageLookupByLibrary.simpleMessage(
      "¡No se pudo abrir la web!",
    ),
    "not_summarized_note": MessageLookupByLibrary.simpleMessage(
      "¡Resumen ausente! Presiona el botón para activar la IA👇",
    ),
    "note": MessageLookupByLibrary.simpleMessage("Nota"),
    "noteX": MessageLookupByLibrary.simpleMessage("NoteX"),
    "noteX_lifetime_essential": MessageLookupByLibrary.simpleMessage(
      "NoteX Essential de por vida",
    ),
    "noteX_pro_lifetime": MessageLookupByLibrary.simpleMessage(
      "NoteX Pro a Vida",
    ),
    "note_404": MessageLookupByLibrary.simpleMessage(
      "Nota no encontrada. Verifica el ID de la nota e inténtalo de nuevo.",
    ),
    "note_not_ready": MessageLookupByLibrary.simpleMessage(
      "La nota no está lista para exportar. Espere a que se complete el procesamiento.",
    ),
    "note_reminders": MessageLookupByLibrary.simpleMessage("Recordatorios"),
    "note_sharing": MessageLookupByLibrary.simpleMessage("Compartir notas"),
    "note_tabs": MessageLookupByLibrary.simpleMessage("Pestañas de notas"),
    "note_taker": MessageLookupByLibrary.simpleMessage("El #1 en notas con IA"),
    "notes": MessageLookupByLibrary.simpleMessage("notas"),
    "notex_empty": MessageLookupByLibrary.simpleMessage("NoteX vacío"),
    "notex_experience": MessageLookupByLibrary.simpleMessage(
      "tu experiencia con NoteX",
    ),
    "nothing_restore": MessageLookupByLibrary.simpleMessage(
      "Nada que restaurar",
    ),
    "noti_default_description": MessageLookupByLibrary.simpleMessage(
      "¡Prepárate y empieza a grabar! 🚀",
    ),
    "noti_default_title": MessageLookupByLibrary.simpleMessage(
      "Es hora de grabar",
    ),
    "noti_req_description": MessageLookupByLibrary.simpleMessage(
      "Las notificaciones pueden incluir alertas, sonidos y distintivos de íconos. Puedes configurarlas en Configuración.",
    ),
    "noti_req_title": MessageLookupByLibrary.simpleMessage(
      "‘NoteX’ Quiere Enviarte Notificaciones",
    ),
    "notifications": MessageLookupByLibrary.simpleMessage("Notificaciones"),
    "notifications_note_created": MessageLookupByLibrary.simpleMessage(
      "Tus notas se han creado correctamente",
    ),
    "notifications_note_ready": MessageLookupByLibrary.simpleMessage(
      "Notificar cuando las notas estén listas",
    ),
    "nova_ai_assistant_mind_mapping": MessageLookupByLibrary.simpleMessage(
      "Asistente Nova IA y Mapas Mentales",
    ),
    "nova_ai_chat": MessageLookupByLibrary.simpleMessage("Chat Nova IA"),
    "nova_chat": MessageLookupByLibrary.simpleMessage("Chat Nova"),
    "of_index": MessageLookupByLibrary.simpleMessage("de"),
    "of_user": MessageLookupByLibrary.simpleMessage(" de los usuarios"),
    "offer_expires": MessageLookupByLibrary.simpleMessage("Oferta termina en"),
    "ok": MessageLookupByLibrary.simpleMessage("OK"),
    "oldest_first": MessageLookupByLibrary.simpleMessage("Más antiguo"),
    "on_track": MessageLookupByLibrary.simpleMessage("mantén rumbo"),
    "on_your_android": MessageLookupByLibrary.simpleMessage(
      "En tu teléfono o tablet Android, abre Google Play Store",
    ),
    "onboarding_generate_audio_video_content":
        MessageLookupByLibrary.simpleMessage("Transformar notas en"),
    "onboarding_generate_audio_video_full_content":
        MessageLookupByLibrary.simpleMessage(
          "Transformar notas en contenido atractivo",
        ),
    "onboarding_generate_audio_video_sub_content":
        MessageLookupByLibrary.simpleMessage("contenido atractivo"),
    "onboarding_generate_audio_video_title":
        MessageLookupByLibrary.simpleMessage("Generar audio y video"),
    "once_in_a_lifetime_offer": MessageLookupByLibrary.simpleMessage(
      "Oferta única",
    ),
    "one_per_day": MessageLookupByLibrary.simpleMessage("1 por día"),
    "one_time_payment": MessageLookupByLibrary.simpleMessage("pago único"),
    "only": MessageLookupByLibrary.simpleMessage("Solo"),
    "only_today": MessageLookupByLibrary.simpleMessage("Solo hoy"),
    "oops_something_went_wrong": MessageLookupByLibrary.simpleMessage(
      "¡Ups!\nAlgo salió mal",
    ),
    "open_now": MessageLookupByLibrary.simpleMessage("Abrir ahora"),
    "open_youtube": MessageLookupByLibrary.simpleMessage("Abrir YouTube"),
    "opportunities": MessageLookupByLibrary.simpleMessage("oportunidades"),
    "or": MessageLookupByLibrary.simpleMessage("o"),
    "or_upper": MessageLookupByLibrary.simpleMessage("O"),
    "organize_assign_action_items": MessageLookupByLibrary.simpleMessage(
      "Organizar tareas",
    ),
    "organize_assign_items": MessageLookupByLibrary.simpleMessage(
      "Organizar tareas",
    ),
    "others": MessageLookupByLibrary.simpleMessage("Otros"),
    "output_language": MessageLookupByLibrary.simpleMessage("Idioma de salida"),
    "pace": MessageLookupByLibrary.simpleMessage("a tu ritmo"),
    "paste": MessageLookupByLibrary.simpleMessage("Pegar"),
    "paste_url_here": MessageLookupByLibrary.simpleMessage("Pegar URL aquí"),
    "paste_youtube_link": MessageLookupByLibrary.simpleMessage(
      "Pegar un enlace de Youtube",
    ),
    "payment_required": MessageLookupByLibrary.simpleMessage(
      "Has usado todos tus créditos gratuitos",
    ),
    "payment_successfully": MessageLookupByLibrary.simpleMessage(
      "Pago exitoso",
    ),
    "pdf_export": MessageLookupByLibrary.simpleMessage("Exportar PDF"),
    "pdf_pdf": MessageLookupByLibrary.simpleMessage("PDF (.pdf)"),
    "per_year": MessageLookupByLibrary.simpleMessage(" por año"),
    "period": MessageLookupByLibrary.simpleMessage(" de %s minutos"),
    "personalized_learning": MessageLookupByLibrary.simpleMessage(
      "Practica a tu manera",
    ),
    "personalized_learning_at": MessageLookupByLibrary.simpleMessage(
      "Practica a tu manera para sacar un 10",
    ),
    "photos": MessageLookupByLibrary.simpleMessage("Fotos"),
    "pick_specific_language": MessageLookupByLibrary.simpleMessage(
      "Elige un idioma específico en el audio para una mejor precisión de transcripción",
    ),
    "plan": MessageLookupByLibrary.simpleMessage("Plan"),
    "please_select_a_language": MessageLookupByLibrary.simpleMessage(
      "¡Selecciona un idioma primero para transcribir con precisión!",
    ),
    "please_select_a_youtube_language": MessageLookupByLibrary.simpleMessage(
      "Por favor, seleccione un idioma para el resumen. Este es el idioma que verá en el resultado del resumen",
    ),
    "please_try_again": MessageLookupByLibrary.simpleMessage(
      "Por favor, inténtalo de nuevo",
    ),
    "please_wait": MessageLookupByLibrary.simpleMessage("Por favor, espera"),
    "podcast": MessageLookupByLibrary.simpleMessage("Podcast"),
    "podcast_name": MessageLookupByLibrary.simpleMessage("Nombre del podcast"),
    "policy": MessageLookupByLibrary.simpleMessage("Política"),
    "premium_features": MessageLookupByLibrary.simpleMessage(
      "Prueba las funciones premium para ver la diferencia",
    ),
    "preparing_video": MessageLookupByLibrary.simpleMessage(
      "Preparando video...",
    ),
    "press_back_again_to_exit": MessageLookupByLibrary.simpleMessage(
      "¡Presiona atrás de nuevo para salir!",
    ),
    "preview_only": MessageLookupByLibrary.simpleMessage(
      "Solo vista previa. El fondo será generado por IA según el contenido",
    ),
    "priority_processing": MessageLookupByLibrary.simpleMessage(
      "Procesamiento prioritario",
    ),
    "privacy_policy": MessageLookupByLibrary.simpleMessage(
      "Política de privacidad",
    ),
    "private": MessageLookupByLibrary.simpleMessage("Privado"),
    "pro": MessageLookupByLibrary.simpleMessage("Plan PRO"),
    "proLite": MessageLookupByLibrary.simpleMessage("Pro Lite"),
    "pro_01": MessageLookupByLibrary.simpleMessage("Pro"),
    "pro_6_hours": MessageLookupByLibrary.simpleMessage(
      "Pro: 6 horas por archivo",
    ),
    "pro_access_life_time": MessageLookupByLibrary.simpleMessage(
      "ACCESO PRO DE POR VIDA",
    ),
    "pro_lifetime": MessageLookupByLibrary.simpleMessage("PRO de por Vida"),
    "process_your_document": MessageLookupByLibrary.simpleMessage(
      "Procesando tu documento...",
    ),
    "process_your_text": MessageLookupByLibrary.simpleMessage(
      "Procesando tu texto...",
    ),
    "processing_content": MessageLookupByLibrary.simpleMessage(
      "Procesando contenido...",
    ),
    "processing_file": MessageLookupByLibrary.simpleMessage(
      "Procesando archivo...",
    ),
    "processing_image": MessageLookupByLibrary.simpleMessage(
      "Procesando imagen...",
    ),
    "processing_note_audio_file": MessageLookupByLibrary.simpleMessage(
      "Procesando audio...",
    ),
    "processing_note_recording": MessageLookupByLibrary.simpleMessage(
      "Procesando grabación...",
    ),
    "processing_note_youtube": MessageLookupByLibrary.simpleMessage(
      "Procesando video de YouTube...",
    ),
    "processing_web_link": MessageLookupByLibrary.simpleMessage(
      "Procesando enlace",
    ),
    "producing_flashcards": MessageLookupByLibrary.simpleMessage(
      "Creando tarjetas con IA...",
    ),
    "professional": MessageLookupByLibrary.simpleMessage("Profesional"),
    "professional_description": MessageLookupByLibrary.simpleMessage(
      "Lenguaje formal adecuado para entorno laboral",
    ),
    "professional_style": MessageLookupByLibrary.simpleMessage("Profesional"),
    "public": MessageLookupByLibrary.simpleMessage("Público"),
    "purchase_fail": MessageLookupByLibrary.simpleMessage(
      "¡Fallo en la compra! Por favor, inténtalo de nuevo.",
    ),
    "purchase_init_fail": MessageLookupByLibrary.simpleMessage(
      "¡Uy! No pudimos iniciar tu compra. Inténtalo de nuevo",
    ),
    "purpose_using": MessageLookupByLibrary.simpleMessage(
      "propósito para usar ",
    ),
    "quarter": MessageLookupByLibrary.simpleMessage("trimestre"),
    "quarterly": MessageLookupByLibrary.simpleMessage("Trimestral"),
    "question": MessageLookupByLibrary.simpleMessage("Pregunta"),
    "quick_access": MessageLookupByLibrary.simpleMessage("Acceso rápido"),
    "quick_import": MessageLookupByLibrary.simpleMessage(
      "O importa rápidamente desde",
    ),
    "quickly": MessageLookupByLibrary.simpleMessage("conceptos"),
    "quiz": MessageLookupByLibrary.simpleMessage("Cuestionario"),
    "quiz_count": MessageLookupByLibrary.simpleMessage(
      "Conteo de cuestionarios",
    ),
    "quiz_diff": MessageLookupByLibrary.simpleMessage(
      "Dificultad del cuestionario",
    ),
    "quiz_gen_success": MessageLookupByLibrary.simpleMessage(
      "Cuestionario generado con éxito",
    ),
    "quiz_iap": MessageLookupByLibrary.simpleMessage(
      "Conjunto de cuestionarios",
    ),
    "quiz_master": MessageLookupByLibrary.simpleMessage(
      "Maestro de Cuestionarios IA",
    ),
    "quiz_score": MessageLookupByLibrary.simpleMessage(
      "Puntuación del cuestionario",
    ),
    "quiz_set": MessageLookupByLibrary.simpleMessage(
      "Conjuntos de cuestionarios",
    ),
    "quiz_set_not_found": MessageLookupByLibrary.simpleMessage(
      "Conjunto de cuestionarios no encontrado",
    ),
    "quizz_for": MessageLookupByLibrary.simpleMessage("Cuestionario para"),
    "quizzes": MessageLookupByLibrary.simpleMessage("Cuestionarios"),
    "rate": MessageLookupByLibrary.simpleMessage("Calificar"),
    "rate_five_stars": MessageLookupByLibrary.simpleMessage(
      "Califícanos con 5 estrellas",
    ),
    "rate_us_on_store": MessageLookupByLibrary.simpleMessage(
      "Califícanos en la tienda",
    ),
    "rating_cmt1": MessageLookupByLibrary.simpleMessage(
      "Esta app es increíble y mejora cada día. Gracias a los desarrolladores por su dedicación. La recomiendo sobre cualquier otra app",
    ),
    "rating_cmt2": MessageLookupByLibrary.simpleMessage(
      "¡Me encanta absolutamente esta aplicación! El compañero perfecto para todas mis reuniones.",
    ),
    "rating_cmt3": MessageLookupByLibrary.simpleMessage(
      "¡Cortó mi tiempo de estudio a la mitad. ¡Más tiempo para descansos de café!",
    ),
    "rating_cmt4": MessageLookupByLibrary.simpleMessage(
      "¡Esta aplicación es absolutamente asombrosa! No solo hace la transcripción, sino que lleva las cosas a otro nivel con resúmenes, esquemas y elementos de acción increíbles. ¡Pura genialidad!",
    ),
    "rating_cmt5": MessageLookupByLibrary.simpleMessage(
      "¡Maravilloso y potente! Todo lo que quieres y más",
    ),
    "rating_cmt6": MessageLookupByLibrary.simpleMessage(
      "Lo probé hoy con una presentación de YouTube. En segundos, transcripción completa, mapa mental y flashcards. Lo usaré diariamente",
    ),
    "rating_cmt7": MessageLookupByLibrary.simpleMessage(
      "La mejor app de notas hasta ahora. Tiene muchas funciones útiles",
    ),
    "rating_cmt8": MessageLookupByLibrary.simpleMessage(
      "Captura cada detalle de las clases de biología. La función de resumen es vital para los exámenes",
    ),
    "rating_sub_context_1": MessageLookupByLibrary.simpleMessage(
      "¡Impresionante!",
    ),
    "rating_sub_context_2": MessageLookupByLibrary.simpleMessage(
      "Reunión profesional",
    ),
    "rating_sub_context_3": MessageLookupByLibrary.simpleMessage(
      "Ahorra Tiempo",
    ),
    "rating_sub_context_4": MessageLookupByLibrary.simpleMessage(
      "Las Mejores Notas de IA",
    ),
    "rating_sub_context_5": MessageLookupByLibrary.simpleMessage(
      "Me encanta esta app",
    ),
    "rating_sub_context_6": MessageLookupByLibrary.simpleMessage(
      "Mejor app de notas IA",
    ),
    "rating_sub_context_7": MessageLookupByLibrary.simpleMessage(
      "La mejor app que he usado",
    ),
    "rating_sub_context_8": MessageLookupByLibrary.simpleMessage(
      "La mejor hasta ahora",
    ),
    "record": MessageLookupByLibrary.simpleMessage("Grabación"),
    "record_audio": MessageLookupByLibrary.simpleMessage("Grabar audio"),
    "record_audio_coming_soon": MessageLookupByLibrary.simpleMessage(
      "Grabar audio (próximamente)",
    ),
    "record_over_x_min": MessageLookupByLibrary.simpleMessage(
      "Grabación superior a %s minutos",
    ),
    "record_over_x_min_details": MessageLookupByLibrary.simpleMessage(
      "Tus grabaciones se guardarán localmente sin transcripciones ni resúmenes de IA. Puedes eliminar los límites para procesarlas al finalizar.",
    ),
    "record_summarize_lecture": MessageLookupByLibrary.simpleMessage(
      "Graba y resume clases universitarias",
    ),
    "recording": MessageLookupByLibrary.simpleMessage("Grabación"),
    "recording_in_progress": MessageLookupByLibrary.simpleMessage(
      "Grabación en curso",
    ),
    "recording_in_progress_content": MessageLookupByLibrary.simpleMessage(
      "Grabando...",
    ),
    "recording_paused": MessageLookupByLibrary.simpleMessage(
      "Grabación en pausa",
    ),
    "recording_paused_content": MessageLookupByLibrary.simpleMessage(
      "Presiona para reanudar",
    ),
    "recording_permission_denied": MessageLookupByLibrary.simpleMessage(
      "¡Permiso de grabación denegado!",
    ),
    "recording_permission_denied_details": MessageLookupByLibrary.simpleMessage(
      "Ve a Configuración para permitir",
    ),
    "recording_quality": MessageLookupByLibrary.simpleMessage(
      "Calidad de grabación",
    ),
    "recording_schedule": MessageLookupByLibrary.simpleMessage(
      "Horario de Grabación",
    ),
    "recording_voice_note": MessageLookupByLibrary.simpleMessage(
      "Grabando nota de voz",
    ),
    "redeem_7_days_for_0": MessageLookupByLibrary.simpleMessage(
      "Canjear 7 días por 0",
    ),
    "redeem_credits": MessageLookupByLibrary.simpleMessage("Canjear"),
    "refer_now": MessageLookupByLibrary.simpleMessage("Referir ahora"),
    "refer_rewards": MessageLookupByLibrary.simpleMessage(
      "Refiere y recompensas",
    ),
    "referral": MessageLookupByLibrary.simpleMessage("Referido"),
    "referral_already_used": MessageLookupByLibrary.simpleMessage(
      "El código de referido ya ha sido usado.",
    ),
    "referral_code": MessageLookupByLibrary.simpleMessage("Código de referido"),
    "referral_credits": MessageLookupByLibrary.simpleMessage(
      "Créditos por Referidos",
    ),
    "referral_not_found": MessageLookupByLibrary.simpleMessage(
      "Código de referido no encontrado.",
    ),
    "referral_self_use": MessageLookupByLibrary.simpleMessage(
      "No puedes usar tu propio código de referido.",
    ),
    "referral_time_expired": MessageLookupByLibrary.simpleMessage(
      "El código de referido ha expirado después de 24 horas.",
    ),
    "referral_validation_err": MessageLookupByLibrary.simpleMessage(
      "Error de validación de referido.",
    ),
    "reload_tap": MessageLookupByLibrary.simpleMessage(
      "Error, toca para recargar",
    ),
    "remain_recording_length": MessageLookupByLibrary.simpleMessage(
      "Quedan 30s - 5min según la duración de la grabación...",
    ),
    "reminders_record_audio": MessageLookupByLibrary.simpleMessage(
      "Configura horarios semanales para grabaciones de audio",
    ),
    "remove_all_limits": MessageLookupByLibrary.simpleMessage(
      "Eliminar todos los límites",
    ),
    "replace": MessageLookupByLibrary.simpleMessage("Reemplazar"),
    "replace_all": MessageLookupByLibrary.simpleMessage("Reemplazar Todo"),
    "report_issue": MessageLookupByLibrary.simpleMessage(
      "¿Cómo reportar un problema?",
    ),
    "report_issue2": MessageLookupByLibrary.simpleMessage(
      "Estamos aquí para ayudarte:",
    ),
    "required": MessageLookupByLibrary.simpleMessage("Ej: Nombre_carpeta A"),
    "reset": MessageLookupByLibrary.simpleMessage("Restaurar"),
    "restart_now": MessageLookupByLibrary.simpleMessage("Reiniciar ahora"),
    "restore": MessageLookupByLibrary.simpleMessage("Restaurar"),
    "restore_fail_message": MessageLookupByLibrary.simpleMessage(
      "Para ayuda, <NAME_EMAIL>",
    ),
    "restore_fail_title": MessageLookupByLibrary.simpleMessage(
      "No hay ítems para restaurar",
    ),
    "restore_purchase": MessageLookupByLibrary.simpleMessage(
      "Restaurar compra",
    ),
    "restore_success_title": MessageLookupByLibrary.simpleMessage(
      "Restauración exitosa",
    ),
    "retention": MessageLookupByLibrary.simpleMessage("y retención"),
    "retention_quickly": MessageLookupByLibrary.simpleMessage(
      "entre conceptos",
    ),
    "retry": MessageLookupByLibrary.simpleMessage("Reintentar"),
    "sale_off": MessageLookupByLibrary.simpleMessage("DESCUENTO"),
    "satisfied": MessageLookupByLibrary.simpleMessage(
      "¡Gracias por tu opinión!",
    ),
    "satisfied_quality": MessageLookupByLibrary.simpleMessage(
      "¿Es útil esta nota?",
    ),
    "save": MessageLookupByLibrary.simpleMessage("Guardar"),
    "save_50": MessageLookupByLibrary.simpleMessage("Ahorra 50%"),
    "save_changes": MessageLookupByLibrary.simpleMessage("¿Guardar cambios?"),
    "save_chat": MessageLookupByLibrary.simpleMessage("Guardar chat"),
    "save_file": MessageLookupByLibrary.simpleMessage("Archivo guardado"),
    "saved_chat": MessageLookupByLibrary.simpleMessage("Chat guardado"),
    "saved_successfully": MessageLookupByLibrary.simpleMessage(
      "Guardado con éxito",
    ),
    "saving_recording": MessageLookupByLibrary.simpleMessage(
      "Guardando grabación en dispositivo",
    ),
    "search": MessageLookupByLibrary.simpleMessage("Buscar"),
    "search_emoji": MessageLookupByLibrary.simpleMessage("Buscar emoji"),
    "search_in_files": MessageLookupByLibrary.simpleMessage(
      "Buscar en archivos",
    ),
    "searching_all_notes": MessageLookupByLibrary.simpleMessage(
      "Buscando en todas las notas",
    ),
    "seconds": MessageLookupByLibrary.simpleMessage("Segundos"),
    "select_a_language": MessageLookupByLibrary.simpleMessage(
      "Selecciona idioma antes de guardar.",
    ),
    "select_all": MessageLookupByLibrary.simpleMessage("Seleccionar todo"),
    "select_and_reorder": MessageLookupByLibrary.simpleMessage(
      "Selecciona y reordena tus módulos de notas. Se requieren al menos 4 pestañas para continuar.",
    ),
    "select_language": MessageLookupByLibrary.simpleMessage(
      "Selecciona idioma",
    ),
    "select_your_note": MessageLookupByLibrary.simpleMessage(
      "Selecciona tus módulos de notas.",
    ),
    "select_your_primary_use_case": MessageLookupByLibrary.simpleMessage(
      "Elige tu uso principal",
    ),
    "server_err": MessageLookupByLibrary.simpleMessage(
      "Ocurrió un error desconocido en el servidor.",
    ),
    "server_error": MessageLookupByLibrary.simpleMessage("Algo salió mal"),
    "setting": MessageLookupByLibrary.simpleMessage("Configuración"),
    "settings": MessageLookupByLibrary.simpleMessage("Configuraciones"),
    "seven_day_free": MessageLookupByLibrary.simpleMessage(
      "7 días gratis, luego",
    ),
    "share": MessageLookupByLibrary.simpleMessage("Enviar a amigos"),
    "share_audio_file": MessageLookupByLibrary.simpleMessage("Compartir audio"),
    "share_code_friends": MessageLookupByLibrary.simpleMessage(
      "Comparte el código con amigos por email, redes sociales o mensajes.",
    ),
    "share_file": MessageLookupByLibrary.simpleMessage(
      "Compartir archivo de audio",
    ),
    "share_note": MessageLookupByLibrary.simpleMessage("Compartir notas"),
    "share_note_link": MessageLookupByLibrary.simpleMessage("Compartir nota"),
    "share_only": MessageLookupByLibrary.simpleMessage("Compartir"),
    "share_referral_code_start_earning_credits":
        MessageLookupByLibrary.simpleMessage(
          "¡Comparte tu código de referido para empezar a ganar créditos!",
        ),
    "share_summary": MessageLookupByLibrary.simpleMessage("Copiar resumen"),
    "share_sync": MessageLookupByLibrary.simpleMessage(
      "Compartir y sincronizar",
    ),
    "share_transcript": MessageLookupByLibrary.simpleMessage(
      "Copiar transcripción",
    ),
    "share_with_link": MessageLookupByLibrary.simpleMessage(
      "Compartir con enlace:",
    ),
    "shared": MessageLookupByLibrary.simpleMessage("Compartido"),
    "sharing_export": MessageLookupByLibrary.simpleMessage(
      "Compartir y exportar",
    ),
    "short": MessageLookupByLibrary.simpleMessage("Breve"),
    "short_description": MessageLookupByLibrary.simpleMessage(
      "Solo puntos clave",
    ),
    "shorts": MessageLookupByLibrary.simpleMessage("Shorts"),
    "show_your_love": MessageLookupByLibrary.simpleMessage(
      "Muestra tu apoyo dándonos una",
    ),
    "signing_in": MessageLookupByLibrary.simpleMessage("Iniciando sesión..."),
    "skip": MessageLookupByLibrary.simpleMessage("Omitir"),
    "slide_count": MessageLookupByLibrary.simpleMessage("Crear diapositivas"),
    "slide_count_tooltip": MessageLookupByLibrary.simpleMessage(
      "Máximo de 12 diapositivas por plantilla",
    ),
    "slide_range": MessageLookupByLibrary.simpleMessage("Crear diapositivas"),
    "slide_show": MessageLookupByLibrary.simpleMessage("Diapositivas"),
    "smart_learning": MessageLookupByLibrary.simpleMessage(
      "Aprendizaje inteligente",
    ),
    "smart_note_big_ideas": MessageLookupByLibrary.simpleMessage(
      "Notas Smart, Grandes Ideas",
    ),
    "smart_quizzes": MessageLookupByLibrary.simpleMessage(
      "Cuestionarios adaptativos ilimitados",
    ),
    "smart_start": MessageLookupByLibrary.simpleMessage(
      "Paquete de Inicio Inteligente",
    ),
    "sort_by": MessageLookupByLibrary.simpleMessage("Ordenar por"),
    "special_gift": MessageLookupByLibrary.simpleMessage("Regalo especial"),
    "special_gift_title": MessageLookupByLibrary.simpleMessage(
      "REGALO ESPECIAL",
    ),
    "special_offer": MessageLookupByLibrary.simpleMessage("OFERTA\nESPECIAL"),
    "speech_language": MessageLookupByLibrary.simpleMessage("Idioma de voz"),
    "start_for_free": MessageLookupByLibrary.simpleMessage("Comienza gratis"),
    "start_free_trial": MessageLookupByLibrary.simpleMessage(
      "Iniciar prueba gratuita",
    ),
    "start_my_7_day_trial": MessageLookupByLibrary.simpleMessage(
      "Inicia mi prueba de 7 días",
    ),
    "start_record": MessageLookupByLibrary.simpleMessage("Iniciar grabación"),
    "start_speaking": MessageLookupByLibrary.simpleMessage("Comienza a Hablar"),
    "step1": MessageLookupByLibrary.simpleMessage(
      "En NoteX, ve a Configuración.",
    ),
    "step2": MessageLookupByLibrary.simpleMessage(
      "Encuentra la versión de la app al final (ej., v1.4.0(6)).",
    ),
    "step3": MessageLookupByLibrary.simpleMessage(
      "Toca la versión 5 veces rápidamente.",
    ),
    "step4": MessageLookupByLibrary.simpleMessage(
      "Tu ID único se copiará automáticamente al portapapeles.",
    ),
    "step5": MessageLookupByLibrary.simpleMessage("En tu mensaje, incluye:"),
    "step51": MessageLookupByLibrary.simpleMessage(
      "Tu ID (pégalo desde el portapapeles).",
    ),
    "step52": MessageLookupByLibrary.simpleMessage(
      "Descripción breve del problema.",
    ),
    "step53": MessageLookupByLibrary.simpleMessage(
      "Detalles relevantes (modelo del dispositivo, versión de iOS).",
    ),
    "step6": MessageLookupByLibrary.simpleMessage("Envíanos un correo a"),
    "student": MessageLookupByLibrary.simpleMessage("Estudiante"),
    "style": MessageLookupByLibrary.simpleMessage("Estilo"),
    "sub_rip": MessageLookupByLibrary.simpleMessage("SubRip (.srt)"),
    "sub_user_have_unlimited": MessageLookupByLibrary.simpleMessage(
      "Los usuarios suscritos tienen uso ilimitado y acceso a todas las funciones premium sin anuncios.\nLos usuarios no suscritos pueden usar la aplicación con anuncios y acceso limitado a funciones premium.\nEl pago se cargará a la cuenta de Google Play al confirmar la compra.\nLa suscripción se renovará automáticamente a menos que se desactive 24 horas antes del final del período actual.\nTu cuenta será cargada según tu plan dentro de las 24 horas previas al final del período actual.\nCualquier parte no utilizada del período de prueba gratuita, si se ofrece, será anulada cuando se compre una suscripción.\nPuedes gestionar o desactivar la renovación automática en la página de suscripción de Google Play después de la compra. Ten en cuenta que desinstalar la app no cancela tu suscripción.",
    ),
    "sub_will_auto_renew": MessageLookupByLibrary.simpleMessage(
      "La suscripción se renovará automáticamente. Cancela en cualquier momento.",
    ),
    "submit": MessageLookupByLibrary.simpleMessage("Enviar"),
    "submit_button": MessageLookupByLibrary.simpleMessage("Enviar"),
    "subscribe": MessageLookupByLibrary.simpleMessage("Suscribirse"),
    "subscribe_via_web": MessageLookupByLibrary.simpleMessage(
      "Si se suscribió vía web, gestione en notexapp.com/setting",
    ),
    "success": MessageLookupByLibrary.simpleMessage("Éxito"),
    "successfully": MessageLookupByLibrary.simpleMessage("Exitosamente"),
    "suggest_features": MessageLookupByLibrary.simpleMessage(
      "Sugerir características",
    ),
    "suggested": MessageLookupByLibrary.simpleMessage("Sugerido"),
    "summarize_video": MessageLookupByLibrary.simpleMessage(
      "Resumir videos largos de YouTube",
    ),
    "summary": MessageLookupByLibrary.simpleMessage("Resumen"),
    "summary_style": MessageLookupByLibrary.simpleMessage("Estilo de Resumen"),
    "summary_successful": MessageLookupByLibrary.simpleMessage(
      "¡Resumen creado con éxito!",
    ),
    "summary_usefulness": MessageLookupByLibrary.simpleMessage(
      "Utilidad del resumen",
    ),
    "supercharge": MessageLookupByLibrary.simpleMessage(
      "Logra más, sin estrés",
    ),
    "support_audio": MessageLookupByLibrary.simpleMessage(
      "Tipos de archivo compatibles: .mp3, .wav, .ogg, .m4a",
    ),
    "support_for_up_to_10_images": MessageLookupByLibrary.simpleMessage(
      "Soporte para hasta 10 imágenes",
    ),
    "support_image": MessageLookupByLibrary.simpleMessage(
      "Tipos de imagen compatibles: .png, .jpg, .heif, .heic",
    ),
    "support_over_onehundred_languages": MessageLookupByLibrary.simpleMessage(
      "Soporte para más de 100 idiomas",
    ),
    "support_youtube_and_more": MessageLookupByLibrary.simpleMessage(
      "Admite YouTube, Web, TikTok, Instagram, Facebook y más",
    ),
    "switch_mode": MessageLookupByLibrary.simpleMessage("Cambiar modo"),
    "sync_from_watch": MessageLookupByLibrary.simpleMessage(
      "Sincronizar desde el reloj",
    ),
    "sync_notes": MessageLookupByLibrary.simpleMessage(
      "Sincroniza notas en tu navegador",
    ),
    "system": MessageLookupByLibrary.simpleMessage("Sistema"),
    "tap_cancel": MessageLookupByLibrary.simpleMessage(
      "Toca Cancelar suscripción",
    ),
    "tap_menu": MessageLookupByLibrary.simpleMessage(
      "Toca Menú > Suscripciones y selecciona la suscripción que deseas cancelar",
    ),
    "tap_the": MessageLookupByLibrary.simpleMessage("Toca el"),
    "tap_the_record": MessageLookupByLibrary.simpleMessage("Pulsa en Grabar"),
    "tap_to_record": MessageLookupByLibrary.simpleMessage(
      "Toca para grabar tus pensamientos",
    ),
    "task_create_err": MessageLookupByLibrary.simpleMessage(
      "Error al crear la tarea. Por favor, inténtalo más tarde.",
    ),
    "templates": MessageLookupByLibrary.simpleMessage("Plantillas"),
    "term_and_cond": MessageLookupByLibrary.simpleMessage(
      "Términos y condiciones",
    ),
    "terms": MessageLookupByLibrary.simpleMessage("Términos"),
    "terms_of_sub": MessageLookupByLibrary.simpleMessage(
      "Términos de suscripción",
    ),
    "terms_of_use": MessageLookupByLibrary.simpleMessage("Términos de uso"),
    "text": MessageLookupByLibrary.simpleMessage("Añadir Texto"),
    "text_must_not_exceed_50_chars": MessageLookupByLibrary.simpleMessage(
      "El texto no debe superar los 50 caracteres",
    ),
    "thank_feedback": MessageLookupByLibrary.simpleMessage(
      "¡Gracias por tus comentarios!",
    ),
    "thinking": MessageLookupByLibrary.simpleMessage("Pensando..."),
    "thirty_min_per": MessageLookupByLibrary.simpleMessage(
      "30 min por \n semana",
    ),
    "this_folder_empty": MessageLookupByLibrary.simpleMessage(
      "¡Hora de crear tu primera nota con IA! ✨",
    ),
    "this_free_trial": MessageLookupByLibrary.simpleMessage(
      "Esta prueba es solo para nuevos usuarios. Disfruta las funciones Pro por una semana.",
    ),
    "this_is_the_language": MessageLookupByLibrary.simpleMessage(
      "Este es el idioma que verá en el resultado del resumen",
    ),
    "thousands_trusted": MessageLookupByLibrary.simpleMessage(
      "Calificado 4.8/5: Confiado por miles",
    ),
    "time": MessageLookupByLibrary.simpleMessage("Hora"),
    "time_black_friday": MessageLookupByLibrary.simpleMessage("22 - 30 Nov"),
    "time_black_friday_2": MessageLookupByLibrary.simpleMessage(
      "22 - 30 Noviembre",
    ),
    "time_out": MessageLookupByLibrary.simpleMessage(
      "Tiempo de espera agotado. Inténtalo de nuevo.",
    ),
    "title": MessageLookupByLibrary.simpleMessage("Título"),
    "title_error_note": MessageLookupByLibrary.simpleMessage(
      "No se pudo crear la nota",
    ),
    "title_success_note": MessageLookupByLibrary.simpleMessage(
      "Nota AI creada con éxito",
    ),
    "to": MessageLookupByLibrary.simpleMessage("a"),
    "to_day": MessageLookupByLibrary.simpleMessage("Hoy"),
    "token_expired": MessageLookupByLibrary.simpleMessage(
      "¡El token ha expirado!",
    ),
    "tolower_credits": MessageLookupByLibrary.simpleMessage("créditos"),
    "tool_tip_language": MessageLookupByLibrary.simpleMessage(
      "Selecciona el idioma principal del discurso antes de guardar esta grabación",
    ),
    "topic_option": MessageLookupByLibrary.simpleMessage("Tema (opcional)"),
    "total": MessageLookupByLibrary.simpleMessage("Total"),
    "transcribing": MessageLookupByLibrary.simpleMessage(
      "Transcribiendo con IA",
    ),
    "transcribing_audio": MessageLookupByLibrary.simpleMessage(
      "Transcribiendo audio...",
    ),
    "transcript": MessageLookupByLibrary.simpleMessage("Transcripción"),
    "transcript_language": MessageLookupByLibrary.simpleMessage(
      "Idioma de transcripción",
    ),
    "transcript_line_cannot_be_empty": MessageLookupByLibrary.simpleMessage(
      "La línea de transcripción no puede estar vacía",
    ),
    "transcript_line_tool_tip": MessageLookupByLibrary.simpleMessage(
      "Haz clic en el ítem de transcripción para editar",
    ),
    "transcription_precision": MessageLookupByLibrary.simpleMessage(
      "Precisión de transcripción",
    ),
    "transform_meetings": MessageLookupByLibrary.simpleMessage(
      "Transforma reuniones",
    ),
    "transform_meetings_into_actionable_intelligence":
        MessageLookupByLibrary.simpleMessage("Reuniones en acciones"),
    "translate_note": MessageLookupByLibrary.simpleMessage("Traducir nota"),
    "translating_note": MessageLookupByLibrary.simpleMessage(
      "Traduciendo nota...",
    ),
    "translation_completed": MessageLookupByLibrary.simpleMessage(
      "Traducción completada",
    ),
    "translation_failed": MessageLookupByLibrary.simpleMessage(
      "Error en la traducción",
    ),
    "trouble_connecting_to_server": MessageLookupByLibrary.simpleMessage(
      "Estamos teniendo problemas para conectarnos al servidor. Por favor, inténtalo de nuevo en un momento.",
    ),
    "try_3_day": MessageLookupByLibrary.simpleMessage("Prueba 3 días gratis"),
    "try_7_day": MessageLookupByLibrary.simpleMessage(
      "Comienza prueba de 7 días",
    ),
    "try_again": MessageLookupByLibrary.simpleMessage(
      "Hubo un problema al generar tus notas. ¡Inténtalo de nuevo!",
    ),
    "try_again_button": MessageLookupByLibrary.simpleMessage(
      "Intentar de nuevo",
    ),
    "try_pro_free_7_day": MessageLookupByLibrary.simpleMessage(
      "Prueba Pro gratis por 7 días",
    ),
    "type_or_paste_any_text_here": MessageLookupByLibrary.simpleMessage(
      "Escribe o pega texto aquí. La IA lo convertirá en un resumen claro con puntos clave.",
    ),
    "uidCopied": m3,
    "unable_download_file": MessageLookupByLibrary.simpleMessage(
      "No se pudo descargar el archivo",
    ),
    "unable_load_audio": MessageLookupByLibrary.simpleMessage(
      "No se pudo cargar el audio:",
    ),
    "unable_share_audio": MessageLookupByLibrary.simpleMessage(
      "No se pudo compartir el archivo de audio",
    ),
    "unable_to_connect_to_server": MessageLookupByLibrary.simpleMessage(
      "Asegúrate de que tu teléfono esté conectado a internet",
    ),
    "unable_to_extract_web_url": MessageLookupByLibrary.simpleMessage(
      "No se puede extraer contenido URL",
    ),
    "unable_to_open_store": MessageLookupByLibrary.simpleMessage(
      "No se puede abrir la tienda",
    ),
    "uncover_opportunities": MessageLookupByLibrary.simpleMessage(
      "oportunidades",
    ),
    "unknown_error": MessageLookupByLibrary.simpleMessage(
      "La aplicación encontró un error desconocido",
    ),
    "unknown_server_error": MessageLookupByLibrary.simpleMessage(
      "¡Ups! Nuestros servidores tuvieron un problema. Inténtalo de nuevo.",
    ),
    "unlimited_ai_chat": MessageLookupByLibrary.simpleMessage(
      "Chat de IA ilimitado, Mapas mentales, Tarjetas de estudio, Cuestionarios",
    ),
    "unlimited_ai_chat_ai_mind_map_flashcard_quiz":
        MessageLookupByLibrary.simpleMessage(
          "Chat AI, Mapas Mentales, Tarjetas y Tests ilimitados",
        ),
    "unlimited_ai_note": MessageLookupByLibrary.simpleMessage(
      "Notas IA ilimitadas de todas las fuentes (YouTube, Documentos, Grabación, Audio)",
    ),
    "unlimited_ai_notes_from_youtube_and_document":
        MessageLookupByLibrary.simpleMessage(
          "Notas AI ilimitadas de YouTube y documentos",
        ),
    "unlimited_audio_youtube_website_to_ai_notes":
        MessageLookupByLibrary.simpleMessage(
          "Audio, YouTube, Docs y Web ilimitados a Notas IA",
        ),
    "unlimited_everything": MessageLookupByLibrary.simpleMessage(
      "Disfruta de notas ilimitadas de IA, servicio prioritario y funciones premium",
    ),
    "unlimited_youtube_document_ai_notes": MessageLookupByLibrary.simpleMessage(
      "Notas IA ilimitadas de YouTube y Docs",
    ),
    "unlock_all_features": MessageLookupByLibrary.simpleMessage(
      "Desbloquear todas las funciones",
    ),
    "unlock_essential_life_time": MessageLookupByLibrary.simpleMessage(
      "Desbloquear Essential Lifetime",
    ),
    "unlock_lifetime_access": MessageLookupByLibrary.simpleMessage(
      "Desbloquea acceso de por vida",
    ),
    "unlock_pro_lifetime": MessageLookupByLibrary.simpleMessage(
      "Desbloquea PRO de por Vida",
    ),
    "unlock_the_most_ipad": MessageLookupByLibrary.simpleMessage(
      "Desbloquea el asistente de notas con IA más potente",
    ),
    "unlock_the_most_powerful_ai_note_taking_assistant":
        MessageLookupByLibrary.simpleMessage(
          "Desbloquea el asistente de notas con IA más potente",
        ),
    "unlock_toge": MessageLookupByLibrary.simpleMessage("DESBLOQUEAR JUNTOS"),
    "unlock_together": MessageLookupByLibrary.simpleMessage(
      "Desbloquear juntos",
    ),
    "unlock_unlimited_access_to_all_ai_features":
        MessageLookupByLibrary.simpleMessage(
          "Desbloquea acceso ilimitado a todas las funciones de IA",
        ),
    "unlock_unlimited_ai": MessageLookupByLibrary.simpleMessage(
      "Desbloquea experiencia IA ilimitada",
    ),
    "unsynced_notes": MessageLookupByLibrary.simpleMessage(
      "Notas no sincronizadas",
    ),
    "update_available": MessageLookupByLibrary.simpleMessage(
      "¡Nueva actualización disponible! Actualiza para disfrutar la mejor experiencia.",
    ),
    "update_failed": MessageLookupByLibrary.simpleMessage(
      "No se pudo actualizar las notas de la comunidad. Por favor, inténtalo de nuevo.",
    ),
    "update_later": MessageLookupByLibrary.simpleMessage("Más tarde"),
    "update_now": MessageLookupByLibrary.simpleMessage("¡Actualizar ahora!"),
    "update_pro": MessageLookupByLibrary.simpleMessage(
      "Actualiza a la experiencia Pro",
    ),
    "update_to_pro": MessageLookupByLibrary.simpleMessage("Actualizar a PRO"),
    "upgrade": MessageLookupByLibrary.simpleMessage("MEJORAR"),
    "upgrade_now": MessageLookupByLibrary.simpleMessage("¡Actualizar ahora!"),
    "upgrade_plan": MessageLookupByLibrary.simpleMessage("Actualizar Plan"),
    "upgrade_to_full_pro_access": MessageLookupByLibrary.simpleMessage(
      "Actualizar a Acceso Pro Completo",
    ),
    "upgrade_to_pro_tier_at_a_special_price":
        MessageLookupByLibrary.simpleMessage(
          "Actualiza a Pro con Precio Especial",
        ),
    "upload": MessageLookupByLibrary.simpleMessage("Subir"),
    "upload_audio": MessageLookupByLibrary.simpleMessage("Subir audio"),
    "upload_audio_file": MessageLookupByLibrary.simpleMessage(
      "Subir archivo de audio",
    ),
    "upload_file": MessageLookupByLibrary.simpleMessage("Subir archivo"),
    "upload_image": MessageLookupByLibrary.simpleMessage("Subir imagen"),
    "upload_in_progress": MessageLookupByLibrary.simpleMessage(
      "Subida en progreso. Mantén la pantalla abierta.",
    ),
    "uploading_to_server": MessageLookupByLibrary.simpleMessage(
      "Subiendo al servidor seguro",
    ),
    "user_disabled": MessageLookupByLibrary.simpleMessage(
      "El usuario de este correo está desactivado.",
    ),
    "user_not_found": MessageLookupByLibrary.simpleMessage(
      "Información de usuario no encontrada.",
    ),
    "verifying_your_credentials": MessageLookupByLibrary.simpleMessage(
      "Verificando tus credenciales",
    ),
    "video": MessageLookupByLibrary.simpleMessage("Video"),
    "video_audio": MessageLookupByLibrary.simpleMessage(
      "De grabaciones, videos y docs a notas con IA",
    ),
    "video_captions": MessageLookupByLibrary.simpleMessage("Subtítulos"),
    "video_is_temporary": MessageLookupByLibrary.simpleMessage(
      "*El video es temporal - Guarda antes de cerrar",
    ),
    "visualize_strategies": MessageLookupByLibrary.simpleMessage(
      "Ver estrategias y",
    ),
    "visualize_strategies_opportunities": MessageLookupByLibrary.simpleMessage(
      "Ver estrategias",
    ),
    "visualize_strategies_uncover": MessageLookupByLibrary.simpleMessage(
      "Ver estrategias y",
    ),
    "visualize_strategies_uncover_opportunities":
        MessageLookupByLibrary.simpleMessage("Visualiza estrategias"),
    "voice": MessageLookupByLibrary.simpleMessage("Voz"),
    "warning_this_ai_note_taking_app_may_cause_excessive_productivity":
        MessageLookupByLibrary.simpleMessage(
          "Advertencia: ¡Esta app de notas con IA puede causar productividad excesiva! 🚀 Usa mi código y ambos obtendremos uso extra. Código: ",
        ),
    "watch_sync_empty_message": MessageLookupByLibrary.simpleMessage(
      "Las grabaciones de tu reloj Apple aparecerán aquí",
    ),
    "web": MessageLookupByLibrary.simpleMessage("Web"),
    "web_link": MessageLookupByLibrary.simpleMessage("Enlace web"),
    "web_sync": MessageLookupByLibrary.simpleMessage("Sincronización web"),
    "website_import": MessageLookupByLibrary.simpleMessage("Importar web"),
    "week": MessageLookupByLibrary.simpleMessage("semana"),
    "week_free_limit": MessageLookupByLibrary.simpleMessage(
      "Límite semanal alcanzado",
    ),
    "weekly": MessageLookupByLibrary.simpleMessage("Semanal"),
    "weekly_free_limit_reached": MessageLookupByLibrary.simpleMessage(
      "Límite semanal alcanzado",
    ),
    "weekly_free_limit_reached_details": MessageLookupByLibrary.simpleMessage(
      "Has usado todas las transcripciones y resúmenes gratuitos de esta semana. Mejora a Pro para acceso ilimitado o espera hasta la próxima semana.",
    ),
    "welcome_notex": MessageLookupByLibrary.simpleMessage(
      "¡Bienvenido a NoteX!",
    ),
    "welcome_title": MessageLookupByLibrary.simpleMessage(
      "Creemos tu primera nota IA",
    ),
    "what_improve": MessageLookupByLibrary.simpleMessage(
      "¿Qué necesitas mejorar?",
    ),
    "whats_new": MessageLookupByLibrary.simpleMessage("Novedades"),
    "word_docx": MessageLookupByLibrary.simpleMessage("Word (.docx)"),
    "work_notes_projects": MessageLookupByLibrary.simpleMessage(
      "Notas y proyectos",
    ),
    "writing_style": MessageLookupByLibrary.simpleMessage(
      "Estilo de Escritura",
    ),
    "wrong": MessageLookupByLibrary.simpleMessage("Incorrecto"),
    "x": MessageLookupByLibrary.simpleMessage("X"),
    "x_skip": MessageLookupByLibrary.simpleMessage("X?"),
    "year": MessageLookupByLibrary.simpleMessage("año"),
    "yearly": MessageLookupByLibrary.simpleMessage("Anual"),
    "yes": MessageLookupByLibrary.simpleMessage("Sí"),
    "yesterday": MessageLookupByLibrary.simpleMessage("Ayer"),
    "you_are_given_a_special_gift_today": MessageLookupByLibrary.simpleMessage(
      "Tienes un regalo especial hoy 🎁",
    ),
    "you_are_pro": MessageLookupByLibrary.simpleMessage("Acceso PRO"),
    "you_can_update_setting": MessageLookupByLibrary.simpleMessage(
      "Puedes actualizarlo en cualquier momento desde Configuración.",
    ),
    "you_have_received": MessageLookupByLibrary.simpleMessage("Has recibido"),
    "you_have_received2": MessageLookupByLibrary.simpleMessage(
      "¡Obtendrás una entrada para ganar acceso Pro de por vida a NoteX! 3 ganadores elegidos el 30 de cada mes 🎁",
    ),
    "you_will_get_one_entry_to_win_noteX": MessageLookupByLibrary.simpleMessage(
      "Obtendrás una entrada para ganar NoteX",
    ),
    "you_will_not_be": MessageLookupByLibrary.simpleMessage(
      "No podrás recuperarlo después",
    ),
    "your_learning": MessageLookupByLibrary.simpleMessage(
      "¡Potencia tu aprendizaje!",
    ),
    "your_learning_device": MessageLookupByLibrary.simpleMessage(
      "Acceso NoteX Pro",
    ),
    "your_note_are_ready": MessageLookupByLibrary.simpleMessage(
      "Tus notas están listas.",
    ),
    "your_personal_study": MessageLookupByLibrary.simpleMessage(
      "Tu estudio personal",
    ),
    "your_personal_study_assistant": MessageLookupByLibrary.simpleMessage(
      "Tu asistente de estudio",
    ),
    "your_plan": MessageLookupByLibrary.simpleMessage("Tu Plan"),
    "your_primary": MessageLookupByLibrary.simpleMessage(
      "¿Cuál es tu principal",
    ),
    "your_product": MessageLookupByLibrary.simpleMessage("TU PRODUCTIVIDAD"),
    "your_recording_will_save": MessageLookupByLibrary.simpleMessage(
      "Tus grabaciones se guardarán localmente sin transcripciones ni resúmenes con IA. Puedes eliminar los límites para procesar esta grabación al completarla.",
    ),
    "your_referrals": MessageLookupByLibrary.simpleMessage("Tus referidos"),
    "youtube": MessageLookupByLibrary.simpleMessage("YouTube"),
    "youtube_import": MessageLookupByLibrary.simpleMessage(
      "Importar de YouTube",
    ),
    "youtube_link": MessageLookupByLibrary.simpleMessage("Enlace de Youtube"),
    "youtube_transcript_language_guidance": MessageLookupByLibrary.simpleMessage(
      "Seleccione idioma de transcripción - Este idioma se usará para generar notas IA",
    ),
    "youtube_video": MessageLookupByLibrary.simpleMessage("Video de Youtube"),
    "youtube_video_note": MessageLookupByLibrary.simpleMessage(
      "Video de YouTube",
    ),
    "yt_credit_err": MessageLookupByLibrary.simpleMessage(
      "Uso gratuito de YouTube insuficiente. Mejora tu plan.",
    ),
    "yt_credit_use_err": MessageLookupByLibrary.simpleMessage(
      "Error al usar el uso gratuito de YouTube. Inténtalo más tarde.",
    ),
    "yt_length_err": MessageLookupByLibrary.simpleMessage(
      "El video de YouTube excede el límite de 10 horas. Por favor, elige un video más corto.",
    ),
    "yt_process_err": MessageLookupByLibrary.simpleMessage(
      "Error al procesar el video de YouTube. Verifica la URL e inténtalo de nuevo.",
    ),
    "yt_sum_limit": MessageLookupByLibrary.simpleMessage(
      "Límite de resumen de YouTube",
    ),
    "z_to_a": MessageLookupByLibrary.simpleMessage("Z-A"),
  };
}
